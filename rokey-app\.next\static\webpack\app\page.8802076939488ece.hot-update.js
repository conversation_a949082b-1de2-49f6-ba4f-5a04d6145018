"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        username: \"@sarahdev\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"Honestly, RouKey changed everything for us. We went from spending hours configuring AI requests to just... not worrying about it anymore. Our AI costs dropped 40% and I actually have time for coffee breaks now.\"\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        username: \"@marcustech\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"I'll be real - I was skeptical at first. But RouKey's failover is like having a safety net you never knew you needed. No more 3AM calls about API limits. My team loves me again.\"\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        username: \"@emilyai\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"300+ models through one API? I thought it was too good to be true. Turns out it's just really good. The analytics help me pick the right model every time. It's like having a crystal ball.\"\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        username: \"@davidpm\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey feels like having an AI whisperer on the team. It just knows which model to use. Our output quality went through the roof and I look like a genius to my boss.\"\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        username: \"@lisaeng\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The analytics dashboard is my new best friend. Finally, data-driven decisions about AI spending that actually make sense. My CFO stopped asking uncomfortable questions.\"\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        username: \"@alexcodes\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"Production-ready from day one. The security features give me peace of mind, and the team management is exactly what we needed. It just works, which is rare these days.\"\n    },\n    {\n        name: \"Maya Patel\",\n        role: \"Startup Founder\",\n        company: \"AI Innovations\",\n        username: \"@mayabuilds\",\n        avatar: \"MP\",\n        image: \"https://images.unsplash.com/photo-1573497620166-aef748c8c792?fm=jpg&q=80&w=400&h=400&fit=crop&crop=face\",\n        content: \"As a solo founder, RouKey is like having a whole AI infrastructure team. I can focus on building my product instead of wrestling with API configurations. Game changer.\"\n    },\n    {\n        name: \"James Wilson\",\n        role: \"Tech Lead\",\n        company: \"DevCorp\",\n        username: \"@jameswilson\",\n        avatar: \"JW\",\n        image: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?fm=jpg&q=80&w=400&h=400&fit=crop&crop=face\",\n        content: \"RouKey's intelligent routing saved our project. We were burning through our AI budget in weeks, now it lasts months. The automatic optimization is pure magic.\"\n    }\n];\n// Testimonial Card Component with mouse tracking\nfunction TestimonialCard(param) {\n    let { testimonial, index } = param;\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseMove = (e)=>{\n        if (!cardRef.current) return;\n        const rect = cardRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        setMousePosition({\n            x,\n            y\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: cardRef,\n        initial: {\n            opacity: 0,\n            x: 50\n        },\n        animate: {\n            opacity: 1,\n            x: 0\n        },\n        transition: {\n            duration: 0.6,\n            delay: index * 0.1\n        },\n        className: \"relative flex-shrink-0 w-[400px] h-[280px] bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 group cursor-pointer overflow-hidden\",\n        onMouseMove: handleMouseMove,\n        style: {\n            background: \"radial-gradient(600px circle at \".concat(mousePosition.x, \"px \").concat(mousePosition.y, \"px, rgba(255, 107, 53, 0.1), transparent 40%)\")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-orange-500/20 blur-sm -z-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-[1px] rounded-2xl bg-gradient-to-br from-gray-900/95 to-gray-800/95 group-hover:from-gray-900/90 group-hover:to-gray-800/90 transition-all duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-base leading-relaxed mb-6 flex-grow\",\n                        children: testimonial.content\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 rounded-full overflow-hidden mr-3 border border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: testimonial.image,\n                                    alt: \"\".concat(testimonial.name, \" profile picture\"),\n                                    width: 40,\n                                    height: 40,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-white text-sm\",\n                                        children: testimonial.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: testimonial.username\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialCard, \"vvki2Zt2ZZAKt4FaPlYc7NZusXs=\");\n_c = TestimonialCard;\nfunction TestimonialsSection() {\n    _s1();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll every 4 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"TestimonialsSection.useEffect.interval\": ()=>{\n                    setCurrentIndex({\n                        \"TestimonialsSection.useEffect.interval\": (prev)=>(prev + 1) % testimonials.length\n                    }[\"TestimonialsSection.useEffect.interval\"]);\n                }\n            }[\"TestimonialsSection.useEffect.interval\"], 4000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>clearInterval(interval)\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], []);\n    // Smooth scroll to current testimonial\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            if (containerRef.current) {\n                const cardWidth = 400 + 24; // card width + gap\n                containerRef.current.scrollTo({\n                    left: currentIndex * cardWidth,\n                    behavior: 'smooth'\n                });\n            }\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        currentIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 bg-[#040716] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-[#1C051C] via-[#040716] to-[#1C051C]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-[#040716] to-transparent z-20 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-[#040716] to-transparent z-20 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: containerRef,\n                    className: \"flex gap-6 px-6 overflow-x-hidden\",\n                    style: {\n                        width: 'calc(100vw + 800px)',\n                        marginLeft: '-400px'\n                    },\n                    children: [\n                        ...testimonials,\n                        ...testimonials\n                    ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialCard, {\n                            testimonial: testimonial,\n                            index: index\n                        }, \"\".concat(testimonial.name, \"-\").concat(index), false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s1(TestimonialsSection, \"8Of9/NU4o97UyR37Hzym87To7bc=\");\n_c1 = TestimonialsSection;\nvar _c, _c1;\n$RefreshReg$(_c, \"TestimonialCard\");\n$RefreshReg$(_c1, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});