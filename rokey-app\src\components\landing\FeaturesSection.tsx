'use client';

import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import EnhancedGridBackground from './EnhancedGridBackground';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  NoSymbolIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Smart AI Routing",
    subtitle: "when you need it",
    description: "<PERSON><PERSON><PERSON>ey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.",
    details: [
      "Intelligent request classification and routing",
      "Automatic model selection based on task type",
      "Real-time performance optimization",
      "Seamless provider switching"
    ],
    bgColor: "bg-blue-600",
    textColor: "text-white",
    subtitleColor: "text-blue-100",
    detailColor: "text-blue-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    subtitle: "military-grade protection",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    details: [
      "AES-256-GCM encryption for all data",
      "Zero-knowledge architecture",
      "SOC 2 Type II compliance",
      "Advanced threat detection"
    ],
    bgColor: "bg-emerald-600",
    textColor: "text-white",
    subtitleColor: "text-emerald-100",
    detailColor: "text-emerald-50"
  },
  {
    icon: ChartBarIcon,
    title: "Cost Optimization",
    subtitle: "intelligent spending",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    details: [
      "Real-time cost tracking and alerts",
      "Automatic free-tier utilization",
      "Budget optimization recommendations",
      "Multi-provider cost comparison"
    ],
    bgColor: "bg-orange-600",
    textColor: "text-white",
    subtitleColor: "text-orange-100",
    detailColor: "text-orange-50"
  },
  {
    icon: GlobeAltIcon,
    title: "300+ AI Models",
    subtitle: "unified access",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    details: [
      "Connect to any AI provider with one API",
      "Automatic failover and load balancing",
      "Real-time performance monitoring",
      "Global infrastructure deployment"
    ],
    bgColor: "bg-purple-600",
    textColor: "text-white",
    subtitleColor: "text-purple-100",
    detailColor: "text-purple-50"
  }
];

export default function FeaturesSection() {
  const [activeCard, setActiveCard] = useState(0);
  const [scrollY, setScrollY] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveCard(prev => (prev + 1) % features.length);
    }, 4000); // Auto-advance every 4 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }
      `}</style>
      <section id="features" className="relative overflow-hidden py-20" ref={sectionRef}>
      {/* Background with RouKey colors */}
      <div className="bg-gradient-to-br from-[#040716] to-[#1C051C] relative">
        {/* Enhanced Grid Background with Parallax */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="absolute inset-0"
          style={{
            transform: `translateY(${scrollY * 0.1}px)`
          }}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center">
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight"
              style={{
                transform: `translateY(${scrollY * 0.05}px)`
              }}
            >
              Enterprise-Grade
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                {' '}AI Infrastructure
              </span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4"
              style={{
                transform: `translateY(${scrollY * 0.03}px)`
              }}
            >
              RouKey provides military-grade security, intelligent routing, and comprehensive analytics
              for the most demanding AI workloads. Built for scale, designed for performance.
            </motion.p>
          </div>

          {/* Feature Cards - Enhanced with Advanced Scroll Effects */}
          <div className="space-y-12">
            {features.map((feature, index) => {
              const cardRef = useRef<HTMLDivElement>(null);
              const isInView = useInView(cardRef, { once: false, margin: "-100px" });

              // Individual card scroll tracking
              const { scrollYProgress: cardProgress } = useScroll({
                target: cardRef,
                offset: ["start end", "end start"]
              });

              // Smooth spring for card animations
              const cardSpring = useSpring(cardProgress, { stiffness: 100, damping: 30 });

              // Advanced scroll transforms for each card
              const cardY = useTransform(cardSpring, [0, 0.5, 1], [50, 0, -50]);
              const cardRotateX = useTransform(cardSpring, [0, 0.5, 1], [5, 0, -5]);
              const cardRotateY = useTransform(cardSpring, [0, 0.5, 1], [-2, 0, 2]);
              const cardScale = useTransform(cardSpring, [0, 0.3, 0.7, 1], [0.8, 1, 1, 0.9]);
              const cardOpacity = useTransform(cardSpring, [0, 0.2, 0.8, 1], [0.3, 1, 1, 0.3]);

              // Magnetic scroll effect - cards slightly follow scroll direction
              const magneticOffset = index % 2 === 0 ? 10 : -10;

              // Wave effect that travels through cards
              const waveDelay = index * 0.1;
              const waveProgress = useTransform(cardSpring, [0, 0.5, 1], [0, 1, 0]);
              const waveScale = useTransform(waveProgress, [0, 1], [1, 1.05]);
              const waveGlow = useTransform(waveProgress, [0, 0.5, 1], [0, 1, 0]);

              // Scroll-based tilt effect
              const tiltX = useTransform(cardSpring, [0, 0.5, 1], [3, 0, -3]);
              const tiltY = useTransform(cardSpring, [0, 0.5, 1], [index % 2 === 0 ? -2 : 2, 0, index % 2 === 0 ? 2 : -2]);

              return (
                <motion.div
                  key={index}
                  ref={cardRef}
                  initial={{ opacity: 0, y: 100, rotateX: 10 }}
                  whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                  viewport={{ once: false, margin: "-50px" }}
                  transition={{
                    duration: 0.8,
                    delay: index * 0.15,
                    type: "spring",
                    stiffness: 100,
                    damping: 20
                  }}
                  className="group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-500 transform-gpu perspective-1000 hover:scale-[1.02] hover:-translate-y-2 cursor-pointer glossy-shine glossy-sweep card-stack card-flip depth-shadow glow-pulse morphing-border scroll-shimmer overflow-hidden"
                  style={{
                    backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',
                    transformStyle: 'preserve-3d',
                    y: cardY,
                    rotateX: tiltX,
                    rotateY: tiltY,
                    scale: waveScale,
                    opacity: cardOpacity
                  }}
                >
                {/* Scroll-triggered wave effect */}
                <motion.div
                  className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                  style={{
                    opacity: waveGlow,
                    x: useTransform(waveProgress, [0, 1], ["-50%", "50%"])
                  }}
                />

                {/* Glossy shine effect */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none" />

                {/* Enhanced glow effect on hover */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />

                {/* Scroll-based particle trail */}
                <motion.div
                  className="absolute top-4 right-4 w-2 h-2 bg-orange-400 rounded-full pointer-events-none"
                  style={{
                    opacity: waveGlow,
                    scale: useTransform(waveGlow, [0, 1], [1, 2]),
                    x: useTransform(waveProgress, [0, 1], [0, 20]),
                    y: useTransform(waveProgress, [0, 1], [0, -10])
                  }}
                />

                {/* Stacking shadow effects */}
                <div className="absolute inset-0 rounded-3xl shadow-xl transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-60 transition-all duration-500"
                     style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)' }} />
                <div className="absolute inset-0 rounded-3xl shadow-lg transform translate-x-2 translate-y-2 -z-20 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-75"
                     style={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }} />

                {/* Card content wrapper with subtle 3D flip effect */}
                <div className="relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  {/* Left Side - Content */}
                  <div>
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                        <feature.icon className="h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-1">
                          {feature.title}
                        </h3>
                        <p className="text-sm text-white/70 font-medium">
                          {feature.subtitle}
                        </p>
                      </div>
                    </div>

                    <p className="text-lg text-white/90 mb-6 leading-relaxed">
                      {feature.description}
                    </p>

                    <ul className="space-y-3">
                      {feature.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start gap-3 text-white/80">
                          <div className="w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2" />
                          <span className="text-sm leading-relaxed">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Right Side - Visual Content */}
                  <div className="flex items-center justify-center">
                    <div className="w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative">
                      {/* Inner glow effect for image container */}
                      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl" />
                      {index === 0 && (
                        <img
                          src="/Smart_AI_Routing.png"
                          alt="Smart AI Routing"
                          className="w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10"
                        />
                      )}
                      {index === 1 && (
                        <img
                          src="/Enterprise_Security.png"
                          alt="Enterprise Security"
                          className="w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10"
                        />
                      )}
                      {index === 2 && (
                        <img
                          src="/Cost_Optimization.png"
                          alt="Cost Optimization"
                          className="w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10"
                        />
                      )}
                      {index === 3 && (
                        <img
                          src="/300+_AI_Models.png"
                          alt="300+ AI Models"
                          className="w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10"
                        />
                      )}
                    </div>
                  </div>
                </div>
                </div>
              </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
    </>
  );
}
