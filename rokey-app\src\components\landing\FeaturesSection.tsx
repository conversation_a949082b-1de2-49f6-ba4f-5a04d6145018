'use client';

import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import EnhancedGridBackground from './EnhancedGridBackground';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  NoSymbolIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Smart AI Routing",
    subtitle: "when you need it",
    description: "<PERSON>ou<PERSON>ey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.",
    details: [
      "Intelligent request classification and routing",
      "Automatic model selection based on task type",
      "Real-time performance optimization",
      "Seamless provider switching"
    ],
    bgColor: "bg-blue-600",
    textColor: "text-white",
    subtitleColor: "text-blue-100",
    detailColor: "text-blue-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    subtitle: "military-grade protection",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    details: [
      "AES-256-GCM encryption for all data",
      "Zero-knowledge architecture",
      "SOC 2 Type II compliance",
      "Advanced threat detection"
    ],
    bgColor: "bg-emerald-600",
    textColor: "text-white",
    subtitleColor: "text-emerald-100",
    detailColor: "text-emerald-50"
  },
  {
    icon: ChartBarIcon,
    title: "Cost Optimization",
    subtitle: "intelligent spending",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    details: [
      "Real-time cost tracking and alerts",
      "Automatic free-tier utilization",
      "Budget optimization recommendations",
      "Multi-provider cost comparison"
    ],
    bgColor: "bg-orange-600",
    textColor: "text-white",
    subtitleColor: "text-orange-100",
    detailColor: "text-orange-50"
  },
  {
    icon: GlobeAltIcon,
    title: "300+ AI Models",
    subtitle: "unified access",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    details: [
      "Connect to any AI provider with one API",
      "Automatic failover and load balancing",
      "Real-time performance monitoring",
      "Global infrastructure deployment"
    ],
    bgColor: "bg-purple-600",
    textColor: "text-white",
    subtitleColor: "text-purple-100",
    detailColor: "text-purple-50"
  }
];

export default function FeaturesSection() {
  const [activeCard, setActiveCard] = useState(0);
  const [scrollY, setScrollY] = useState(0);
  const [cardVisibility, setCardVisibility] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);

      // Calculate card visibility based on scroll position
      if (cardRefs.current.length > 0) {
        const viewportHeight = window.innerHeight;
        const viewportTop = window.scrollY;
        const viewportBottom = viewportTop + viewportHeight;
        const viewportCenter = viewportTop + viewportHeight / 2;

        const visibilityScores = cardRefs.current.map((cardRef, index) => {
          if (!cardRef) return 0;

          const rect = cardRef.getBoundingClientRect();
          const cardTop = rect.top + window.scrollY;
          const cardBottom = cardTop + rect.height;
          const cardCenter = cardTop + rect.height / 2;

          // Check if card is in viewport
          const isInViewport = cardBottom > viewportTop && cardTop < viewportBottom;

          if (!isInViewport) return 0;

          // Calculate how much of the card is visible
          const visibleTop = Math.max(cardTop, viewportTop);
          const visibleBottom = Math.min(cardBottom, viewportBottom);
          const visibleHeight = Math.max(0, visibleBottom - visibleTop);
          const visibilityRatio = visibleHeight / rect.height;

          // Calculate distance from viewport center for focus effect
          const distanceFromCenter = Math.abs(cardCenter - viewportCenter);
          const maxDistance = viewportHeight / 2;
          const centerProximity = Math.max(0, 1 - (distanceFromCenter / maxDistance));

          // Combine visibility ratio and center proximity
          return visibilityRatio * 0.3 + centerProximity * 0.7;
        });

        setCardVisibility(visibilityScores);

        // Debug: Log visibility scores
        console.log('Card visibility scores:', visibilityScores.map((score, i) => `Card ${i}: ${score.toFixed(2)}`));
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);
    handleScroll(); // Initial calculation

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveCard(prev => (prev + 1) % features.length);
    }, 4000); // Auto-advance every 4 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <style jsx global>{`
        .perspective-1000 {
          perspective: 1000px;
        }

        /* Glossy card effect */
        .glossy-card {
          position: relative;
          overflow: hidden;
          transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .glossy-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          transition: left 0.6s ease;
          z-index: 1;
          pointer-events: none;
        }

        .glossy-card:hover::before {
          left: 100%;
        }

        .glossy-card::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.15) 0%,
            transparent 50%,
            rgba(255, 255, 255, 0.08) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 1;
          pointer-events: none;
        }

        .glossy-card:hover::after {
          opacity: 1;
        }

        .glossy-card:hover {
          box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.4),
                      0 0 0 1px rgba(255, 255, 255, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
          border-color: rgba(255, 255, 255, 0.3) !important;
        }

        /* Card content should be above the glossy effects */
        .card-content {
          position: relative;
          z-index: 2;
        }
      `}</style>
      <section id="features" className="relative overflow-hidden py-20" ref={sectionRef}>
      {/* Background with RouKey colors */}
      <div className="bg-gradient-to-br from-[#040716] to-[#1C051C] relative">
        {/* Enhanced Grid Background with Parallax */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="absolute inset-0"
          style={{
            transform: `translateY(${scrollY * 0.1}px)`
          }}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center">
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight"
              style={{
                transform: `translateY(${scrollY * 0.05}px)`
              }}
            >
              Enterprise-Grade
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                {' '}AI Infrastructure
              </span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4"
              style={{
                transform: `translateY(${scrollY * 0.03}px)`
              }}
            >
              RouKey provides military-grade security, intelligent routing, and comprehensive analytics
              for the most demanding AI workloads. Built for scale, designed for performance.
            </motion.p>
          </div>

          {/* Feature Cards - N8N Style */}
          <div className="space-y-12">
            {features.map((feature, index) => {
              const visibility = cardVisibility[index] || 0;
              const scale = 0.75 + (visibility * 0.25); // Scale from 0.75 to 1.0 (more pronounced)
              const cardOpacity = 0.3 + (visibility * 0.7); // Opacity from 0.3 to 1.0 (more pronounced)

              // Enhanced glow for most visible card
              const glowIntensity = visibility > 0.7 ? visibility : 0;
              const baseColor = index === 0 ? '37, 99, 235' : index === 1 ? '5, 150, 105' : index === 2 ? '234, 88, 12' : '147, 51, 234';

              return (
                <motion.div
                  key={index}
                  ref={(el) => { cardRefs.current[index] = el; }}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="glossy-card rounded-3xl p-8 shadow-2xl border border-white/10"
                  style={{
                    backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',
                    transform: `scale(${scale})`,
                    opacity: cardOpacity,
                    boxShadow: `0 25px 50px -12px rgba(0, 0, 0, 0.25),
                               0 0 0 1px rgba(255, 255, 255, 0.1),
                               inset 0 1px 0 rgba(255, 255, 255, 0.1),
                               0 0 ${20 + glowIntensity * 30}px rgba(${baseColor}, ${glowIntensity * 0.4})`,
                    borderColor: `rgba(255, 255, 255, ${0.1 + glowIntensity * 0.2})`
                  }}
                >
                  <div className="card-content">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                      {/* Left Side - Content */}
                      <div>
                        <div className="flex items-center gap-4 mb-6">
                          <div className="w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center">
                            <feature.icon className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-2xl font-bold text-white mb-1">
                              {feature.title}
                            </h3>
                            <p className="text-sm text-white/70 font-medium">
                              {feature.subtitle}
                            </p>
                          </div>
                        </div>

                        <p className="text-lg text-white/90 mb-6 leading-relaxed">
                          {feature.description}
                        </p>

                        <ul className="space-y-3">
                          {feature.details.map((detail, idx) => (
                            <li key={idx} className="flex items-start gap-3 text-white/80">
                              <div className="w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2" />
                              <span className="text-sm leading-relaxed">{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Right Side - Visual Content */}
                      <div className="flex items-center justify-center">
                        <div className="w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden">
                          {index === 0 && (
                            <img
                              src="/Smart_AI_Routing.png"
                              alt="Smart AI Routing"
                              className="w-full h-full object-cover rounded-2xl"
                            />
                          )}
                          {index === 1 && (
                            <img
                              src="/Enterprise_Security.png"
                              alt="Enterprise Security"
                              className="w-full h-full object-cover rounded-2xl"
                            />
                          )}
                          {index === 2 && (
                            <img
                              src="/Cost_Optimization.png"
                              alt="Cost Optimization"
                              className="w-full h-full object-cover rounded-2xl"
                            />
                          )}
                          {index === 3 && (
                            <img
                              src="/300+_AI_Models.png"
                              alt="300+ AI Models"
                              className="w-full h-full object-cover rounded-2xl"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
    </>
  );
}
