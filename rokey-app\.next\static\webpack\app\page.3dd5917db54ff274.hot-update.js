"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToAnimation: () => (/* binding */ attachToAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\nfunction attachToAnimation(animation, options) {\n    const timeline = (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.getTimeline)(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWFuaW1hdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ1U7O0FBRXZEO0FBQ0EscUJBQXFCLG9FQUFXO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDJEQUFlO0FBQ2xDO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1hbmltYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG9ic2VydmVUaW1lbGluZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG5mdW5jdGlvbiBhdHRhY2hUb0FuaW1hdGlvbihhbmltYXRpb24sIG9wdGlvbnMpIHtcbiAgICBjb25zdCB0aW1lbGluZSA9IGdldFRpbWVsaW5lKG9wdGlvbnMpO1xuICAgIHJldHVybiBhbmltYXRpb24uYXR0YWNoVGltZWxpbmUoe1xuICAgICAgICB0aW1lbGluZTogb3B0aW9ucy50YXJnZXQgPyB1bmRlZmluZWQgOiB0aW1lbGluZSxcbiAgICAgICAgb2JzZXJ2ZTogKHZhbHVlQW5pbWF0aW9uKSA9PiB7XG4gICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi5wYXVzZSgpO1xuICAgICAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZSgocHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi50aW1lID0gdmFsdWVBbmltYXRpb24uZHVyYXRpb24gKiBwcm9ncmVzcztcbiAgICAgICAgICAgIH0sIHRpbWVsaW5lKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToFunction: () => (/* binding */ attachToFunction)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)(onScroll, (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__.getTimeline)(options));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWZ1bmN0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBQ0o7QUFDYzs7QUFFdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzREFBVTtBQUN6QjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZUFBZSwyREFBZSxXQUFXLG9FQUFXO0FBQ3BEO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1mdW5jdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyBzY3JvbGxJbmZvIH0gZnJvbSAnLi90cmFjay5tanMnO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG4vKipcbiAqIElmIHRoZSBvblNjcm9sbCBmdW5jdGlvbiBoYXMgdHdvIGFyZ3VtZW50cywgaXQncyBleHBlY3RpbmdcbiAqIG1vcmUgc3BlY2lmaWMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIHNjcm9sbCBmcm9tIHNjcm9sbEluZm8uXG4gKi9cbmZ1bmN0aW9uIGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkge1xuICAgIHJldHVybiBvblNjcm9sbC5sZW5ndGggPT09IDI7XG59XG5mdW5jdGlvbiBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zKSB7XG4gICAgaWYgKGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkpIHtcbiAgICAgICAgcmV0dXJuIHNjcm9sbEluZm8oKGluZm8pID0+IHtcbiAgICAgICAgICAgIG9uU2Nyb2xsKGluZm9bb3B0aW9ucy5heGlzXS5wcm9ncmVzcywgaW5mbyk7XG4gICAgICAgIH0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZShvblNjcm9sbCwgZ2V0VGltZWxpbmUob3B0aW9ucykpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9GdW5jdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: () => (/* binding */ scroll)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./attach-animation.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\");\n/* harmony import */ var _attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./attach-function.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\");\n\n\n\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? (0,_attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__.attachToFunction)(onScroll, optionsWithDefaults)\n        : (0,_attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__.attachToAnimation)(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDdUI7QUFDRjs7QUFFekQsNEJBQTRCLGdFQUFnRSxJQUFJO0FBQ2hHO0FBQ0EsZUFBZSw4Q0FBSTtBQUNuQixrQ0FBa0M7QUFDbEM7QUFDQSxVQUFVLHNFQUFnQjtBQUMxQixVQUFVLHdFQUFpQjtBQUMzQjs7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHNjcm9sbFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vb3AgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfSBmcm9tICcuL2F0dGFjaC1hbmltYXRpb24ubWpzJztcbmltcG9ydCB7IGF0dGFjaFRvRnVuY3Rpb24gfSBmcm9tICcuL2F0dGFjaC1mdW5jdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBzY3JvbGwob25TY3JvbGwsIHsgYXhpcyA9IFwieVwiLCBjb250YWluZXIgPSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50LCAuLi5vcHRpb25zIH0gPSB7fSkge1xuICAgIGlmICghY29udGFpbmVyKVxuICAgICAgICByZXR1cm4gbm9vcDtcbiAgICBjb25zdCBvcHRpb25zV2l0aERlZmF1bHRzID0geyBheGlzLCBjb250YWluZXIsIC4uLm9wdGlvbnMgfTtcbiAgICByZXR1cm4gdHlwZW9mIG9uU2Nyb2xsID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKVxuICAgICAgICA6IGF0dGFjaFRvQW5pbWF0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKTtcbn1cblxuZXhwb3J0IHsgc2Nyb2xsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: () => (/* binding */ createScrollInfo),\n/* harmony export */   updateScrollInfo: () => (/* binding */ updateScrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs\");\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: () => (/* binding */ namedEdges),\n/* harmony export */   resolveEdge: () => (/* binding */ resolveEdge)\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: () => (/* binding */ resolveOffsets)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/clamp.mjs\");\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.clamp)(0, 1, info[axis].interpolate(info[axis].current));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: () => (/* binding */ calcInset)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: () => (/* binding */ resolveOffset)\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: () => (/* binding */ ScrollOffset)\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFxvZmZzZXRzXFxwcmVzZXRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: () => (/* binding */ createOnScrollHandler)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: () => (/* binding */ scrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(motion_dom__WEBPACK_IMPORTED_MODULE_3__.frameData.timestamp);\n            }\n            motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(listener, false, true);\n    return () => {\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimeline: () => (/* binding */ getTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n\n\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.supportsScrollTimeline)()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLDRDQUE0QyxJQUFJO0FBQzlGLHFCQUFxQiwyREFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcdmlld3BvcnRcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNvbHZlRWxlbWVudHMgfSBmcm9tICdtb3Rpb24tZG9tJztcblxuY29uc3QgdGhyZXNob2xkcyA9IHtcbiAgICBzb21lOiAwLFxuICAgIGFsbDogMSxcbn07XG5mdW5jdGlvbiBpblZpZXcoZWxlbWVudE9yU2VsZWN0b3IsIG9uU3RhcnQsIHsgcm9vdCwgbWFyZ2luOiByb290TWFyZ2luLCBhbW91bnQgPSBcInNvbWVcIiB9ID0ge30pIHtcbiAgICBjb25zdCBlbGVtZW50cyA9IHJlc29sdmVFbGVtZW50cyhlbGVtZW50T3JTZWxlY3Rvcik7XG4gICAgY29uc3QgYWN0aXZlSW50ZXJzZWN0aW9ucyA9IG5ldyBXZWFrTWFwKCk7XG4gICAgY29uc3Qgb25JbnRlcnNlY3Rpb25DaGFuZ2UgPSAoZW50cmllcykgPT4ge1xuICAgICAgICBlbnRyaWVzLmZvckVhY2goKGVudHJ5KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBvbkVuZCA9IGFjdGl2ZUludGVyc2VjdGlvbnMuZ2V0KGVudHJ5LnRhcmdldCk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoZXJlJ3Mgbm8gY2hhbmdlIHRvIHRoZSBpbnRlcnNlY3Rpb24sIHdlIGRvbid0IG5lZWQgdG9cbiAgICAgICAgICAgICAqIGRvIGFueXRoaW5nIGhlcmUuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmIChlbnRyeS5pc0ludGVyc2VjdGluZyA9PT0gQm9vbGVhbihvbkVuZCkpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgaWYgKGVudHJ5LmlzSW50ZXJzZWN0aW5nKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3T25FbmQgPSBvblN0YXJ0KGVudHJ5LnRhcmdldCwgZW50cnkpO1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgbmV3T25FbmQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgICAgICBhY3RpdmVJbnRlcnNlY3Rpb25zLnNldChlbnRyeS50YXJnZXQsIG5ld09uRW5kKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIG9ic2VydmVyLnVub2JzZXJ2ZShlbnRyeS50YXJnZXQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiBvbkVuZCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgb25FbmQoZW50cnkpO1xuICAgICAgICAgICAgICAgIGFjdGl2ZUludGVyc2VjdGlvbnMuZGVsZXRlKGVudHJ5LnRhcmdldCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIob25JbnRlcnNlY3Rpb25DaGFuZ2UsIHtcbiAgICAgICAgcm9vdCxcbiAgICAgICAgcm9vdE1hcmdpbixcbiAgICAgICAgdGhyZXNob2xkOiB0eXBlb2YgYW1vdW50ID09PSBcIm51bWJlclwiID8gYW1vdW50IDogdGhyZXNob2xkc1thbW91bnRdLFxuICAgIH0pO1xuICAgIGVsZW1lbnRzLmZvckVhY2goKGVsZW1lbnQpID0+IG9ic2VydmVyLm9ic2VydmUoZWxlbWVudCkpO1xuICAgIHJldHVybiAoKSA9PiBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG59XG5cbmV4cG9ydCB7IGluVmlldyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false, initial = false, } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIsdURBQXVELElBQUk7QUFDckYsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdXRpbHNcXHVzZS1pbi12aWV3Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5WaWV3IH0gZnJvbSAnLi4vcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5mdW5jdGlvbiB1c2VJblZpZXcocmVmLCB7IHJvb3QsIG1hcmdpbiwgYW1vdW50LCBvbmNlID0gZmFsc2UsIGluaXRpYWwgPSBmYWxzZSwgfSA9IHt9KSB7XG4gICAgY29uc3QgW2lzSW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoaW5pdGlhbCk7XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFyZWYuY3VycmVudCB8fCAob25jZSAmJiBpc0luVmlldykpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGNvbnN0IG9uRW50ZXIgPSAoKSA9PiB7XG4gICAgICAgICAgICBzZXRJblZpZXcodHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gb25jZSA/IHVuZGVmaW5lZCA6ICgpID0+IHNldEluVmlldyhmYWxzZSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICByb290OiAocm9vdCAmJiByb290LmN1cnJlbnQpIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIG1hcmdpbixcbiAgICAgICAgICAgIGFtb3VudCxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIGluVmlldyhyZWYuY3VycmVudCwgb25FbnRlciwgb3B0aW9ucyk7XG4gICAgfSwgW3Jvb3QsIHJlZiwgbWFyZ2luLCBvbmNlLCBhbW91bnRdKTtcbiAgICByZXR1cm4gaXNJblZpZXc7XG59XG5cbmV4cG9ydCB7IHVzZUluVmlldyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1jb21wdXRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29tYmluZU1vdGlvblZhbHVlcyB9IGZyb20gJy4vdXNlLWNvbWJpbmUtdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNvbXB1dGVkKGNvbXB1dGUpIHtcbiAgICAvKipcbiAgICAgKiBPcGVuIHNlc3Npb24gb2YgY29sbGVjdE1vdGlvblZhbHVlcy4gQW55IE1vdGlvblZhbHVlIHRoYXQgY2FsbHMgZ2V0KClcbiAgICAgKiB3aWxsIGJlIHNhdmVkIGludG8gdGhpcyBhcnJheS5cbiAgICAgKi9cbiAgICBjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQgPSBbXTtcbiAgICBjb21wdXRlKCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VDb21iaW5lTW90aW9uVmFsdWVzKGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCwgY29tcHV0ZSk7XG4gICAgLyoqXG4gICAgICogU3luY2hyb25vdXNseSBjbG9zZSBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlQ29tcHV0ZWQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: () => (/* binding */ useScroll)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__.scroll)((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-spring.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSpring: () => (/* binding */ useSpring)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n\n\n\n\n\n\nfunction useSpring(source, options = {}) {\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionConfigContext);\n    const getFromSource = () => ((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isMotionValue)(source) ? source.get() : source);\n    // isStatic will never change, allowing early hooks return\n    if (isStatic) {\n        return (0,_use_transform_mjs__WEBPACK_IMPORTED_MODULE_3__.useTransform)(getFromSource);\n    }\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.useMotionValue)(getFromSource());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_5__.attachSpring)(value, source, options);\n    }, [value, JSON.stringify(options)]);\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXNwcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF5RDtBQUNGO0FBQ2tCO0FBQ2pCO0FBQ0w7O0FBRW5ELHVDQUF1QztBQUN2QyxZQUFZLFdBQVcsRUFBRSxpREFBVSxDQUFDLGlGQUFtQjtBQUN2RCxpQ0FBaUMseURBQWE7QUFDOUM7QUFDQTtBQUNBLGVBQWUsZ0VBQVk7QUFDM0I7QUFDQSxrQkFBa0IscUVBQWM7QUFDaEMsSUFBSSx5REFBa0I7QUFDdEIsZUFBZSx3REFBWTtBQUMzQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1zcHJpbmcubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGF0dGFjaFNwcmluZywgaXNNb3Rpb25WYWx1ZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29udGV4dCwgdXNlSW5zZXJ0aW9uRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTW90aW9uQ29uZmlnQ29udGV4dCB9IGZyb20gJy4uL2NvbnRleHQvTW90aW9uQ29uZmlnQ29udGV4dC5tanMnO1xuaW1wb3J0IHsgdXNlTW90aW9uVmFsdWUgfSBmcm9tICcuL3VzZS1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IHVzZVRyYW5zZm9ybSB9IGZyb20gJy4vdXNlLXRyYW5zZm9ybS5tanMnO1xuXG5mdW5jdGlvbiB1c2VTcHJpbmcoc291cmNlLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IGlzU3RhdGljIH0gPSB1c2VDb250ZXh0KE1vdGlvbkNvbmZpZ0NvbnRleHQpO1xuICAgIGNvbnN0IGdldEZyb21Tb3VyY2UgPSAoKSA9PiAoaXNNb3Rpb25WYWx1ZShzb3VyY2UpID8gc291cmNlLmdldCgpIDogc291cmNlKTtcbiAgICAvLyBpc1N0YXRpYyB3aWxsIG5ldmVyIGNoYW5nZSwgYWxsb3dpbmcgZWFybHkgaG9va3MgcmV0dXJuXG4gICAgaWYgKGlzU3RhdGljKSB7XG4gICAgICAgIHJldHVybiB1c2VUcmFuc2Zvcm0oZ2V0RnJvbVNvdXJjZSk7XG4gICAgfVxuICAgIGNvbnN0IHZhbHVlID0gdXNlTW90aW9uVmFsdWUoZ2V0RnJvbVNvdXJjZSgpKTtcbiAgICB1c2VJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgICByZXR1cm4gYXR0YWNoU3ByaW5nKHZhbHVlLCBzb3VyY2UsIG9wdGlvbnMpO1xuICAgIH0sIFt2YWx1ZSwgSlNPTi5zdHJpbmdpZnkob3B0aW9ucyldKTtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZVNwcmluZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/handle-element.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: () => (/* binding */ resizeElement)\n/* harmony export */ });\n/* harmony import */ var _utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-svg-element.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-svg-element.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/resolve-elements.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n    if (borderBoxSize && borderBoxSize[0]) {\n        return borderBoxSize[0][(borderBoxAxis + \"Size\")];\n    }\n    else if ((0,_utils_is_svg_element_mjs__WEBPACK_IMPORTED_MODULE_0__.isSVGElement)(target) && \"getBBox\" in target) {\n        return target.getBBox()[svgAxis];\n    }\n    else {\n        return target[htmlAxis];\n    }\n};\nconst getWidth = /*@__PURE__*/ getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/ getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({ target, borderBoxSize }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler(target, {\n            get width() {\n                return getWidth(target, borderBoxSize);\n            },\n            get height() {\n                return getHeight(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/handle-window.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: () => (/* binding */ resizeWindow)\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const info = {\n            get width() {\n                return window.innerWidth;\n            },\n            get height() {\n                return window.innerHeight;\n            },\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size &&\n            typeof windowResizeHandler === \"function\") {\n            window.removeEventListener(\"resize\", windowResizeHandler);\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvcmVzaXplL2hhbmRsZS13aW5kb3cubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xccmVzaXplXFxoYW5kbGUtd2luZG93Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3aW5kb3dDYWxsYmFja3MgPSBuZXcgU2V0KCk7XG5sZXQgd2luZG93UmVzaXplSGFuZGxlcjtcbmZ1bmN0aW9uIGNyZWF0ZVdpbmRvd1Jlc2l6ZUhhbmRsZXIoKSB7XG4gICAgd2luZG93UmVzaXplSGFuZGxlciA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIGdldCB3aWR0aCgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gd2luZG93LmlubmVyV2lkdGg7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IGhlaWdodCgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gd2luZG93LmlubmVySGVpZ2h0O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmZvckVhY2goKGNhbGxiYWNrKSA9PiBjYWxsYmFjayhpbmZvKSk7XG4gICAgfTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCB3aW5kb3dSZXNpemVIYW5kbGVyKTtcbn1cbmZ1bmN0aW9uIHJlc2l6ZVdpbmRvdyhjYWxsYmFjaykge1xuICAgIHdpbmRvd0NhbGxiYWNrcy5hZGQoY2FsbGJhY2spO1xuICAgIGlmICghd2luZG93UmVzaXplSGFuZGxlcilcbiAgICAgICAgY3JlYXRlV2luZG93UmVzaXplSGFuZGxlcigpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIHdpbmRvd0NhbGxiYWNrcy5kZWxldGUoY2FsbGJhY2spO1xuICAgICAgICBpZiAoIXdpbmRvd0NhbGxiYWNrcy5zaXplICYmXG4gICAgICAgICAgICB0eXBlb2Ygd2luZG93UmVzaXplSGFuZGxlciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCB3aW5kb3dSZXNpemVIYW5kbGVyKTtcbiAgICAgICAgICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICB9O1xufVxuXG5leHBvcnQgeyByZXNpemVXaW5kb3cgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/resize/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: () => (/* binding */ resize)\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvcmVzaXplL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7QUFDRjs7QUFFbkQ7QUFDQSxxQ0FBcUMsZ0VBQVksTUFBTSxrRUFBYTtBQUNwRTs7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xccmVzaXplXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzaXplRWxlbWVudCB9IGZyb20gJy4vaGFuZGxlLWVsZW1lbnQubWpzJztcbmltcG9ydCB7IHJlc2l6ZVdpbmRvdyB9IGZyb20gJy4vaGFuZGxlLXdpbmRvdy5tanMnO1xuXG5mdW5jdGlvbiByZXNpemUoYSwgYikge1xuICAgIHJldHVybiB0eXBlb2YgYSA9PT0gXCJmdW5jdGlvblwiID8gcmVzaXplV2luZG93KGEpIDogcmVzaXplRWxlbWVudChhLCBiKTtcbn1cblxuZXhwb3J0IHsgcmVzaXplIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/scroll/observe.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeTimeline: () => (/* binding */ observeTimeline)\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.preUpdate(onFrame, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(onFrame);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvc2Nyb2xsL29ic2VydmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTREOztBQUU1RDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksdURBQUs7QUFDVCxpQkFBaUIsaUVBQVc7QUFDNUI7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHNjcm9sbFxcb2JzZXJ2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJhbWUsIGNhbmNlbEZyYW1lIH0gZnJvbSAnLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbmZ1bmN0aW9uIG9ic2VydmVUaW1lbGluZSh1cGRhdGUsIHRpbWVsaW5lKSB7XG4gICAgbGV0IHByZXZQcm9ncmVzcztcbiAgICBjb25zdCBvbkZyYW1lID0gKCkgPT4ge1xuICAgICAgICBjb25zdCB7IGN1cnJlbnRUaW1lIH0gPSB0aW1lbGluZTtcbiAgICAgICAgY29uc3QgcGVyY2VudGFnZSA9IGN1cnJlbnRUaW1lID09PSBudWxsID8gMCA6IGN1cnJlbnRUaW1lLnZhbHVlO1xuICAgICAgICBjb25zdCBwcm9ncmVzcyA9IHBlcmNlbnRhZ2UgLyAxMDA7XG4gICAgICAgIGlmIChwcmV2UHJvZ3Jlc3MgIT09IHByb2dyZXNzKSB7XG4gICAgICAgICAgICB1cGRhdGUocHJvZ3Jlc3MpO1xuICAgICAgICB9XG4gICAgICAgIHByZXZQcm9ncmVzcyA9IHByb2dyZXNzO1xuICAgIH07XG4gICAgZnJhbWUucHJlVXBkYXRlKG9uRnJhbWUsIHRydWUpO1xuICAgIHJldHVybiAoKSA9PiBjYW5jZWxGcmFtZShvbkZyYW1lKTtcbn1cblxuZXhwb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHRyYW5zZm9ybS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJwb2xhdGUgfSBmcm9tICcuL2ludGVycG9sYXRlLm1qcyc7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybSguLi5hcmdzKSB7XG4gICAgY29uc3QgdXNlSW1tZWRpYXRlID0gIUFycmF5LmlzQXJyYXkoYXJnc1swXSk7XG4gICAgY29uc3QgYXJnT2Zmc2V0ID0gdXNlSW1tZWRpYXRlID8gMCA6IC0xO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBhcmdzWzAgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGlucHV0UmFuZ2UgPSBhcmdzWzEgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG91dHB1dFJhbmdlID0gYXJnc1syICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvcHRpb25zID0gYXJnc1szICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnRlcnBvbGF0b3IgPSBpbnRlcnBvbGF0ZShpbnB1dFJhbmdlLCBvdXRwdXRSYW5nZSwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/value/spring-value.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachSpring: () => (/* binding */ attachSpring),\n/* harmony export */   springValue: () => (/* binding */ springValue)\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/JSAnimation.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/JSAnimation.mjs\");\n/* harmony import */ var _utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n    const initialValue = (0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source) ? source.get() : source;\n    const value = (0,_index_mjs__WEBPACK_IMPORTED_MODULE_1__.motionValue)(initialValue);\n    attachSpring(value, source, options);\n    return value;\n}\nfunction attachSpring(value, source, options) {\n    const initialValue = value.get();\n    let activeAnimation = null;\n    let latestValue = initialValue;\n    let latestSetter;\n    const unit = typeof initialValue === \"string\"\n        ? initialValue.replace(/[\\d.-]/g, \"\")\n        : undefined;\n    const stopAnimation = () => {\n        if (activeAnimation) {\n            activeAnimation.stop();\n            activeAnimation = null;\n        }\n    };\n    const startAnimation = () => {\n        stopAnimation();\n        activeAnimation = new _animation_JSAnimation_mjs__WEBPACK_IMPORTED_MODULE_2__.JSAnimation({\n            keyframes: [asNumber(value.get()), asNumber(latestValue)],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...options,\n            onUpdate: latestSetter,\n        });\n    };\n    value.attach((v, set) => {\n        latestValue = v;\n        latestSetter = (latest) => set(parseValue(latest, unit));\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.postRender(startAnimation);\n        return value.get();\n    }, stopAnimation);\n    let unsubscribe = undefined;\n    if ((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(source)) {\n        unsubscribe = source.on(\"change\", (v) => value.set(parseValue(v, unit)));\n        value.on(\"destroy\", unsubscribe);\n    }\n    return unsubscribe;\n}\nfunction parseValue(v, unit) {\n    return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n    return typeof v === \"number\" ? v : parseFloat(v);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    var _s1 = $RefreshSig$();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1fa32eff8cf0f851\",\n                children: \".perspective-1000.jsx-1fa32eff8cf0f851{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-12\",\n                                    children: features.map(_s1((feature, index)=>{\n                                        _s1();\n                                        const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n                                        const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView)(cardRef, {\n                                            once: false,\n                                            margin: \"-100px\"\n                                        });\n                                        // Individual card scroll tracking\n                                        const { scrollYProgress: cardProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n                                            target: cardRef,\n                                            offset: [\n                                                \"start end\",\n                                                \"end start\"\n                                            ]\n                                        });\n                                        // Smooth spring for card animations\n                                        const cardSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(cardProgress, {\n                                            stiffness: 100,\n                                            damping: 30\n                                        });\n                                        // Advanced scroll transforms for each card\n                                        const cardY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            50,\n                                            0,\n                                            -50\n                                        ]);\n                                        const cardRotateX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            5,\n                                            0,\n                                            -5\n                                        ]);\n                                        const cardRotateY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            -2,\n                                            0,\n                                            2\n                                        ]);\n                                        const cardScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.3,\n                                            0.7,\n                                            1\n                                        ], [\n                                            0.8,\n                                            1,\n                                            1,\n                                            0.9\n                                        ]);\n                                        const cardOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.2,\n                                            0.8,\n                                            1\n                                        ], [\n                                            0.3,\n                                            1,\n                                            1,\n                                            0.3\n                                        ]);\n                                        // Magnetic scroll effect - cards slightly follow scroll direction\n                                        const magneticY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(scrollY, [\n                                            0,\n                                            1000,\n                                            2000,\n                                            3000\n                                        ], [\n                                            0,\n                                            index % 2 === 0 ? -10 : 10,\n                                            index % 2 === 0 ? 10 : -10,\n                                            0\n                                        ]);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: cardRef,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 100,\n                                                rotateX: 10\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0,\n                                                rotateX: 0\n                                            },\n                                            viewport: {\n                                                once: false,\n                                                margin: \"-50px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.15,\n                                                type: \"spring\",\n                                                stiffness: 100,\n                                                damping: 20\n                                            },\n                                            className: \"group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-500 transform-gpu perspective-1000 hover:scale-[1.02] hover:-translate-y-2 cursor-pointer glossy-shine glossy-sweep card-stack card-flip depth-shadow glow-pulse overflow-hidden\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transformStyle: 'preserve-3d',\n                                                y: cardY,\n                                                rotateX: cardRotateX,\n                                                rotateY: cardRotateY,\n                                                scale: cardScale,\n                                                opacity: cardOpacity,\n                                                transform: \"translateY(\".concat(magneticY, \"px)\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.2)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-xl transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-60 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.1)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-lg transform translate-x-2 translate-y-2 -z-20 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-75\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                    className: \"h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 238,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                        children: feature.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 241,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                        children: feature.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 244,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-3\",\n                                                                        children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 257,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                        children: detail\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 258,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 256,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 268,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Smart_AI_Routing.png\",\n                                                                            alt: \"Smart AI Routing\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Enterprise_Security.png\",\n                                                                            alt: \"Enterprise Security\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Cost_Optimization.png\",\n                                                                            alt: \"Cost Optimization\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/300+_AI_Models.png\",\n                                                                            alt: \"300+ AI Models\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this);\n                                    }, \"5iogkGm1syDu2LSYzi/6JghoFco=\", false, function() {\n                                        return [\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n                                        ];\n                                    }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"e/ymO/RETnm/0eb4/vH+s2wwC3g=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});