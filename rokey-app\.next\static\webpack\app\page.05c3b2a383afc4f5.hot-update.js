"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/CTASection.tsx":
/*!***********************************************!*\
  !*** ./src/components/landing/CTASection.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTASection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CTASection() {\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const handleMouseMove = (e)=>{\n        if (containerRef.current) {\n            const rect = containerRef.current.getBoundingClientRect();\n            setMousePosition({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-white/5 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: containerRef,\n                    onMouseMove: handleMouseMove,\n                    className: \"relative bg-gradient-to-br from-gray-800/40 to-gray-900/60 rounded-3xl p-12 border border-white/10 backdrop-blur-sm overflow-hidden group\",\n                    style: {\n                        background: \"\\n              radial-gradient(600px circle at \".concat(mousePosition.x, \"px \").concat(mousePosition.y, \"px,\\n                rgba(255, 255, 255, 0.06),\\n                transparent 40%),\\n              linear-gradient(135deg, rgba(107, 114, 128, 0.4), rgba(17, 24, 39, 0.6))\\n            \")\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\",\n                            style: {\n                                background: \"radial-gradient(300px circle at \".concat(mousePosition.x, \"px \").concat(mousePosition.y, \"px,\\n                rgba(255, 255, 255, 0.1),\\n                transparent 50%)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight\",\n                                    children: [\n                                        \"Scale your AI infrastructure\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"without the complexity.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: 0.05\n                                    },\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300 mb-2\",\n                                            children: \"300+ AI models, intelligent routing, zero vendor lock-in.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300\",\n                                            children: [\n                                                \"Ready to transform your AI workflow? \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#ff6b35] hover:text-[#f7931e] transition-colors cursor-pointer\",\n                                                    children: \"Start building\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 54\n                                                }, this),\n                                                \" in minutes.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: 0.1\n                                    },\n                                    className: \"mb-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/auth/signup?plan=professional\",\n                                        className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105 transform relative overflow-hidden group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"Start building\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CTASection.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(CTASection, \"D6apWrYaCQQUBtQkEnJ/EERDzF4=\");\n_c = CTASection;\nvar _c;\n$RefreshReg$(_c, \"CTASection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/CTASection.tsx\n"));

/***/ })

});