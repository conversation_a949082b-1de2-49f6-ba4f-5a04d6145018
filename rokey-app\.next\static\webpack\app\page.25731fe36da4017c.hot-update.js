"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [cardVisibility, setCardVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                    // Calculate card visibility based on scroll position\n                    if (cardRefs.current.length > 0) {\n                        const viewportHeight = window.innerHeight;\n                        const viewportTop = window.scrollY;\n                        const viewportBottom = viewportTop + viewportHeight;\n                        const viewportCenter = viewportTop + viewportHeight / 2;\n                        const visibilityScores = cardRefs.current.map({\n                            \"FeaturesSection.useEffect.handleScroll.visibilityScores\": (cardRef, index)=>{\n                                if (!cardRef) return 0;\n                                const rect = cardRef.getBoundingClientRect();\n                                const cardTop = rect.top + window.scrollY;\n                                const cardBottom = cardTop + rect.height;\n                                const cardCenter = cardTop + rect.height / 2;\n                                // Check if card is in viewport\n                                const isInViewport = cardBottom > viewportTop && cardTop < viewportBottom;\n                                if (!isInViewport) return 0;\n                                // Calculate how much of the card is visible\n                                const visibleTop = Math.max(cardTop, viewportTop);\n                                const visibleBottom = Math.min(cardBottom, viewportBottom);\n                                const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n                                const visibilityRatio = visibleHeight / rect.height;\n                                // Calculate distance from viewport center for focus effect\n                                const distanceFromCenter = Math.abs(cardCenter - viewportCenter);\n                                const maxDistance = viewportHeight / 2;\n                                const centerProximity = Math.max(0, 1 - distanceFromCenter / maxDistance);\n                                // Combine visibility ratio and center proximity\n                                return visibilityRatio * 0.3 + centerProximity * 0.7;\n                            }\n                        }[\"FeaturesSection.useEffect.handleScroll.visibilityScores\"]);\n                        setCardVisibility(visibilityScores);\n                        // Debug: Log visibility scores\n                        console.log('Card visibility scores:', visibilityScores.map({\n                            \"FeaturesSection.useEffect.handleScroll\": (score, i)=>\"Card \".concat(i, \": \").concat(score.toFixed(2))\n                        }[\"FeaturesSection.useEffect.handleScroll\"]));\n                    }\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            window.addEventListener('resize', handleScroll);\n            handleScroll(); // Initial calculation\n            return ({\n                \"FeaturesSection.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    window.removeEventListener('resize', handleScroll);\n                }\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"79d68e2849b71ee5\",\n                children: '.perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.glossy-card{position:relative;overflow:hidden;-webkit-transition:all.5s cubic-bezier(.4,0,.2,1);-moz-transition:all.5s cubic-bezier(.4,0,.2,1);-o-transition:all.5s cubic-bezier(.4,0,.2,1);transition:all.5s cubic-bezier(.4,0,.2,1)}.glossy-card::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);-webkit-transition:left.6s ease;-moz-transition:left.6s ease;-o-transition:left.6s ease;transition:left.6s ease;z-index:1;pointer-events:none}.glossy-card:hover::before{left:100%}.glossy-card::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-moz-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-o-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:linear-gradient(135deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);opacity:0;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;z-index:1;pointer-events:none}.glossy-card:hover::after{opacity:1}.glossy-card:hover{-webkit-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;-moz-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;border-color:rgba(255,255,255,.3)!important}.card-content{position:relative;z-index:2}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-12\",\n                                    children: features.map((feature, index)=>{\n                                        const visibility = cardVisibility[index] || 0;\n                                        const scale = 0.75 + visibility * 0.25; // Scale from 0.75 to 1.0 (more pronounced)\n                                        const cardOpacity = 0.3 + visibility * 0.7; // Opacity from 0.3 to 1.0 (more pronounced)\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: (el)=>{\n                                                cardRefs.current[index] = el;\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"glossy-card rounded-3xl p-8 shadow-2xl border border-white/10\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transform: \"scale(\".concat(scale, \")\"),\n                                                opacity: cardOpacity,\n                                                boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                className: \"h-6 w-6 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                    children: feature.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                    children: feature.subtitle\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-3\",\n                                                                    children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                    children: detail\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 328,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Smart_AI_Routing.png\",\n                                                                        alt: \"Smart AI Routing\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Enterprise_Security.png\",\n                                                                        alt: \"Enterprise Security\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Cost_Optimization.png\",\n                                                                        alt: \"Cost Optimization\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/300+_AI_Models.png\",\n                                                                        alt: \"300+ AI Models\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"LyzPP6iq6w4fKeBfhPlXvuCGEpA=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});