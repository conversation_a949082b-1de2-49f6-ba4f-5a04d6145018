"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [cardVisibility, setCardVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                    // Calculate card visibility based on scroll position\n                    if (cardRefs.current.length > 0) {\n                        const viewportHeight = window.innerHeight;\n                        const viewportCenter = window.scrollY + viewportHeight / 2;\n                        const visibilityScores = cardRefs.current.map({\n                            \"FeaturesSection.useEffect.handleScroll.visibilityScores\": (cardRef, index)=>{\n                                if (!cardRef) return 0;\n                                const rect = cardRef.getBoundingClientRect();\n                                const cardTop = window.scrollY + rect.top;\n                                const cardBottom = cardTop + rect.height;\n                                const cardCenter = cardTop + rect.height / 2;\n                                // Calculate how close the card center is to viewport center\n                                const distance = Math.abs(cardCenter - viewportCenter);\n                                const maxDistance = viewportHeight;\n                                // Convert distance to visibility score (0-1, where 1 is most visible)\n                                const visibility = Math.max(0, 1 - distance / maxDistance);\n                                return visibility;\n                            }\n                        }[\"FeaturesSection.useEffect.handleScroll.visibilityScores\"]);\n                        setCardVisibility(visibilityScores);\n                    }\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            handleScroll(); // Initial calculation\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"bf3f87e5c69d299f\",\n                children: '.perspective-1000.jsx-bf3f87e5c69d299f{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.glossy-card.jsx-bf3f87e5c69d299f{position:relative;overflow:hidden}.glossy-card.jsx-bf3f87e5c69d299f::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.1),transparent);-webkit-transition:left.6s ease;-moz-transition:left.6s ease;-o-transition:left.6s ease;transition:left.6s ease;z-index:1}.glossy-card.jsx-bf3f87e5c69d299f:hover::before{left:100%}.glossy-card.jsx-bf3f87e5c69d299f::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(255,255,255,.1)0%,transparent 50%,rgba(255,255,255,.05)100%);background:-moz-linear-gradient(315deg,rgba(255,255,255,.1)0%,transparent 50%,rgba(255,255,255,.05)100%);background:-o-linear-gradient(315deg,rgba(255,255,255,.1)0%,transparent 50%,rgba(255,255,255,.05)100%);background:linear-gradient(135deg,rgba(255,255,255,.1)0%,transparent 50%,rgba(255,255,255,.05)100%);opacity:0;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;z-index:1;pointer-events:none}.glossy-card.jsx-bf3f87e5c69d299f:hover::after{opacity:1}.card-content.jsx-bf3f87e5c69d299f{position:relative;z-index:2}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"space-y-12\",\n                                    children: features.map((feature, index)=>{\n                                        const visibility = cardVisibility[index] || 0;\n                                        const scale = 0.85 + visibility * 0.15; // Scale from 0.85 to 1.0\n                                        const opacity = 0.4 + visibility * 0.6; // Opacity from 0.4 to 1.0\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: (el)=>{\n                                                cardRefs.current[index] = el;\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"glossy-card rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/20 transition-all duration-500 hover:shadow-3xl\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transform: \"scale(\".concat(scale, \")\"),\n                                                opacity: opacity,\n                                                boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-bf3f87e5c69d299f\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                className: \"h-6 w-6 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 279,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-bf3f87e5c69d299f\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                    children: feature.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 282,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                    children: feature.subtitle\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 285,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"space-y-3\",\n                                                                    children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 298,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                    children: detail\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Smart_AI_Routing.png\",\n                                                                        alt: \"Smart AI Routing\",\n                                                                        className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Enterprise_Security.png\",\n                                                                        alt: \"Enterprise Security\",\n                                                                        className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Cost_Optimization.png\",\n                                                                        alt: \"Cost Optimization\",\n                                                                        className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/300+_AI_Models.png\",\n                                                                        alt: \"300+ AI Models\",\n                                                                        className: \"jsx-bf3f87e5c69d299f\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"LyzPP6iq6w4fKeBfhPlXvuCGEpA=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});