"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    var _s1 = $RefreshSig$();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1fa32eff8cf0f851\",\n                children: \".perspective-1000.jsx-1fa32eff8cf0f851{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-12\",\n                                    children: features.map(_s1((feature, index)=>{\n                                        _s1();\n                                        const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n                                        const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView)(cardRef, {\n                                            once: false,\n                                            margin: \"-100px\"\n                                        });\n                                        // Individual card scroll tracking\n                                        const { scrollYProgress: cardProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n                                            target: cardRef,\n                                            offset: [\n                                                \"start end\",\n                                                \"end start\"\n                                            ]\n                                        });\n                                        // Smooth spring for card animations\n                                        const cardSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(cardProgress, {\n                                            stiffness: 100,\n                                            damping: 30\n                                        });\n                                        // Advanced scroll transforms for each card\n                                        const cardY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            50,\n                                            0,\n                                            -50\n                                        ]);\n                                        const cardRotateX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            5,\n                                            0,\n                                            -5\n                                        ]);\n                                        const cardRotateY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            -2,\n                                            0,\n                                            2\n                                        ]);\n                                        const cardScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.3,\n                                            0.7,\n                                            1\n                                        ], [\n                                            0.8,\n                                            1,\n                                            1,\n                                            0.9\n                                        ]);\n                                        const cardOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.2,\n                                            0.8,\n                                            1\n                                        ], [\n                                            0.3,\n                                            1,\n                                            1,\n                                            0.3\n                                        ]);\n                                        // Magnetic scroll effect - cards slightly follow scroll direction\n                                        const magneticOffset = index % 2 === 0 ? 10 : -10;\n                                        // Wave effect that travels through cards\n                                        const waveDelay = index * 0.1;\n                                        const waveProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            0,\n                                            1,\n                                            0\n                                        ]);\n                                        const waveScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(waveProgress, [\n                                            0,\n                                            1\n                                        ], [\n                                            1,\n                                            1.05\n                                        ]);\n                                        const waveGlow = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(waveProgress, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            0,\n                                            1,\n                                            0\n                                        ]);\n                                        // Scroll-based tilt effect\n                                        const tiltX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            3,\n                                            0,\n                                            -3\n                                        ]);\n                                        const tiltY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            index % 2 === 0 ? -2 : 2,\n                                            0,\n                                            index % 2 === 0 ? 2 : -2\n                                        ]);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: cardRef,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 100,\n                                                rotateX: 10\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0,\n                                                rotateX: 0\n                                            },\n                                            viewport: {\n                                                once: false,\n                                                margin: \"-50px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.15,\n                                                type: \"spring\",\n                                                stiffness: 100,\n                                                damping: 20\n                                            },\n                                            className: \"group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-500 transform-gpu perspective-1000 hover:scale-[1.02] hover:-translate-y-2 cursor-pointer glossy-shine glossy-sweep card-stack card-flip depth-shadow glow-pulse morphing-border scroll-shimmer overflow-hidden\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transformStyle: 'preserve-3d',\n                                                y: cardY,\n                                                rotateX: tiltX,\n                                                rotateY: tiltY,\n                                                scale: waveScale,\n                                                opacity: cardOpacity\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\",\n                                                    style: {\n                                                        opacity: waveGlow,\n                                                        x: (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(waveProgress, [\n                                                            0,\n                                                            1\n                                                        ], [\n                                                            \"-50%\",\n                                                            \"50%\"\n                                                        ])\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute top-4 right-4 w-2 h-2 bg-orange-400 rounded-full pointer-events-none\",\n                                                    style: {\n                                                        opacity: waveGlow,\n                                                        scale: waveGlow * 2,\n                                                        x: waveProgress * 20,\n                                                        y: waveProgress * -10\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.2)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-xl transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-60 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.1)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-lg transform translate-x-2 translate-y-2 -z-20 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-75\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                    className: \"h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 264,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                        children: feature.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                        children: feature.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 270,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-3\",\n                                                                        children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 283,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                        children: detail\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Smart_AI_Routing.png\",\n                                                                            alt: \"Smart AI Routing\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Enterprise_Security.png\",\n                                                                            alt: \"Enterprise Security\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Cost_Optimization.png\",\n                                                                            alt: \"Cost Optimization\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/300+_AI_Models.png\",\n                                                                            alt: \"300+ AI Models\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this);\n                                    }, \"Rv3dlrrNUNP2Pp8vZ+vAs8LvxaE=\", false, function() {\n                                        return [\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n                                        ];\n                                    }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"e/ymO/RETnm/0eb4/vH+s2wwC3g=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});