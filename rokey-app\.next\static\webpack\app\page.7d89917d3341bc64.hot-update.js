"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        username: \"@sarahdev\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"Honestly, RouKey changed everything for us. We went from spending hours configuring AI requests to just... not worrying about it anymore. Our AI costs dropped 40% and I actually have time for coffee breaks now.\"\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        username: \"@marcustech\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"I'll be real - I was skeptical at first. But RouKey's failover is like having a safety net you never knew you needed. No more 3AM calls about API limits. My team loves me again.\"\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        username: \"@emilyai\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"300+ models through one API? I thought it was too good to be true. Turns out it's just really good. The analytics help me pick the right model every time. It's like having a crystal ball.\"\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        username: \"@davidpm\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey feels like having an AI whisperer on the team. It just knows which model to use. Our output quality went through the roof and I look like a genius to my boss.\"\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        username: \"@lisaeng\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The analytics dashboard is my new best friend. Finally, data-driven decisions about AI spending that actually make sense. My CFO stopped asking uncomfortable questions.\"\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        username: \"@alexcodes\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"Production-ready from day one. The security features give me peace of mind, and the team management is exactly what we needed. It just works, which is rare these days.\"\n    },\n    {\n        name: \"Maya Patel\",\n        role: \"Startup Founder\",\n        company: \"AI Innovations\",\n        username: \"@mayabuilds\",\n        avatar: \"MP\",\n        image: \"/maya.jpg\",\n        content: \"As a solo founder, RouKey is like having a whole AI infrastructure team. I can focus on building my product instead of wrestling with API configurations. Game changer.\"\n    },\n    {\n        name: \"James Wilson\",\n        role: \"Tech Lead\",\n        company: \"DevCorp\",\n        username: \"@jameswilson\",\n        avatar: \"JW\",\n        image: \"/james.jpg\",\n        content: \"RouKey's intelligent routing saved our project. We were burning through our AI budget in weeks, now it lasts months. The automatic optimization is pure magic.\"\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedGridBackground, {\n                gridSize: 50,\n                opacity: 0.05,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Trusted by Developers Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: 0.05\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            ...Array(testimonial.rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarIcon, {\n                                                className: \"h-5 w-5 text-[#ff6b35]\"\n                                            }, i, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-6 leading-relaxed\",\n                                        children: [\n                                            '\"',\n                                            testimonial.content,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: testimonial.image,\n                                                    alt: \"\".concat(testimonial.name, \" profile picture\"),\n                                                    width: 48,\n                                                    height: 48,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            testimonial.role,\n                                                            \" at \",\n                                                            testimonial.company\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, testimonial.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});