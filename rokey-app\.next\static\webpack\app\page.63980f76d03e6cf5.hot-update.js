"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [cardVisibility, setCardVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                    // Calculate card visibility based on scroll position\n                    if (cardRefs.current.length > 0) {\n                        const viewportHeight = window.innerHeight;\n                        const viewportTop = window.scrollY;\n                        const viewportBottom = viewportTop + viewportHeight;\n                        const viewportCenter = viewportTop + viewportHeight / 2;\n                        const visibilityScores = cardRefs.current.map({\n                            \"FeaturesSection.useEffect.handleScroll.visibilityScores\": (cardRef, index)=>{\n                                if (!cardRef) return 0;\n                                const rect = cardRef.getBoundingClientRect();\n                                const cardTop = rect.top + window.scrollY;\n                                const cardBottom = cardTop + rect.height;\n                                const cardCenter = cardTop + rect.height / 2;\n                                // Check if card is in viewport\n                                const isInViewport = cardBottom > viewportTop && cardTop < viewportBottom;\n                                if (!isInViewport) return 0;\n                                // Calculate how much of the card is visible\n                                const visibleTop = Math.max(cardTop, viewportTop);\n                                const visibleBottom = Math.min(cardBottom, viewportBottom);\n                                const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n                                const visibilityRatio = visibleHeight / rect.height;\n                                // Calculate distance from viewport center for focus effect\n                                const distanceFromCenter = Math.abs(cardCenter - viewportCenter);\n                                const maxDistance = viewportHeight / 2;\n                                const centerProximity = Math.max(0, 1 - distanceFromCenter / maxDistance);\n                                // Combine visibility ratio and center proximity\n                                return visibilityRatio * 0.3 + centerProximity * 0.7;\n                            }\n                        }[\"FeaturesSection.useEffect.handleScroll.visibilityScores\"]);\n                        setCardVisibility(visibilityScores);\n                    }\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            window.addEventListener('resize', handleScroll);\n            handleScroll(); // Initial calculation\n            return ({\n                \"FeaturesSection.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    window.removeEventListener('resize', handleScroll);\n                }\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"79d68e2849b71ee5\",\n                children: '.perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.glossy-card{position:relative;overflow:hidden;-webkit-transition:all.5s cubic-bezier(.4,0,.2,1);-moz-transition:all.5s cubic-bezier(.4,0,.2,1);-o-transition:all.5s cubic-bezier(.4,0,.2,1);transition:all.5s cubic-bezier(.4,0,.2,1)}.glossy-card::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);-webkit-transition:left.6s ease;-moz-transition:left.6s ease;-o-transition:left.6s ease;transition:left.6s ease;z-index:1;pointer-events:none}.glossy-card:hover::before{left:100%}.glossy-card::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-moz-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-o-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:linear-gradient(135deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);opacity:0;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;z-index:1;pointer-events:none}.glossy-card:hover::after{opacity:1}.glossy-card:hover{-webkit-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;-moz-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;border-color:rgba(255,255,255,.3)!important}.card-content{position:relative;z-index:2}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-12\",\n                                    children: features.map((feature, index)=>{\n                                        const visibility = cardVisibility[index] || 0;\n                                        const scale = 0.85 + visibility * 0.15; // Scale from 0.85 to 1.0\n                                        const cardOpacity = 0.4 + visibility * 0.6; // Opacity from 0.4 to 1.0\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: (el)=>{\n                                                cardRefs.current[index] = el;\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"glossy-card rounded-3xl p-8 shadow-2xl border border-white/10\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transform: \"scale(\".concat(scale, \")\"),\n                                                opacity: cardOpacity,\n                                                boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                className: \"h-6 w-6 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                    children: feature.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                    children: feature.subtitle\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-3\",\n                                                                    children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 324,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                    children: detail\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 325,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Smart_AI_Routing.png\",\n                                                                        alt: \"Smart AI Routing\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Enterprise_Security.png\",\n                                                                        alt: \"Enterprise Security\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Cost_Optimization.png\",\n                                                                        alt: \"Cost Optimization\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/300+_AI_Models.png\",\n                                                                        alt: \"300+ AI Models\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"LyzPP6iq6w4fKeBfhPlXvuCGEpA=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});