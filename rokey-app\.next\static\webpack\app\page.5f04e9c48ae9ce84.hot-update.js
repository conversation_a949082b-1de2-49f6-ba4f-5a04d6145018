"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    var _s1 = $RefreshSig$();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1fa32eff8cf0f851\",\n                children: \".perspective-1000.jsx-1fa32eff8cf0f851{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-12\",\n                                    children: features.map(_s1((feature, index)=>{\n                                        _s1();\n                                        const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n                                        const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView)(cardRef, {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        });\n                                        // Simplified scroll tracking - only when needed\n                                        const { scrollYProgress: cardProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n                                            target: cardRef,\n                                            offset: [\n                                                \"start end\",\n                                                \"end start\"\n                                            ]\n                                        });\n                                        // Reduced transforms for better performance\n                                        const cardY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            20,\n                                            0,\n                                            -20\n                                        ]);\n                                        const cardScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0,\n                                            0.3,\n                                            0.7,\n                                            1\n                                        ], [\n                                            0.95,\n                                            1,\n                                            1,\n                                            0.95\n                                        ]);\n                                        // Simple wave effect - less computation\n                                        const waveGlow = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0.3,\n                                            0.5,\n                                            0.7\n                                        ], [\n                                            0,\n                                            0.5,\n                                            0\n                                        ]);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: cardRef,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-50px\"\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1,\n                                                ease: \"easeOut\"\n                                            },\n                                            className: \"group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-300 hover:scale-[1.01] hover:-translate-y-1 cursor-pointer glossy-shine overflow-hidden\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                y: cardY,\n                                                scale: cardScale\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\",\n                                                    style: {\n                                                        opacity: waveGlow,\n                                                        x: (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(waveProgress, [\n                                                            0,\n                                                            1\n                                                        ], [\n                                                            \"-50%\",\n                                                            \"50%\"\n                                                        ])\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute top-4 right-4 w-2 h-2 bg-orange-400 rounded-full pointer-events-none\",\n                                                    style: {\n                                                        opacity: waveGlow,\n                                                        scale: (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(waveGlow, [\n                                                            0,\n                                                            1\n                                                        ], [\n                                                            1,\n                                                            2\n                                                        ]),\n                                                        x: (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(waveProgress, [\n                                                            0,\n                                                            1\n                                                        ], [\n                                                            0,\n                                                            20\n                                                        ]),\n                                                        y: (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(waveProgress, [\n                                                            0,\n                                                            1\n                                                        ], [\n                                                            0,\n                                                            -10\n                                                        ])\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.2)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-xl transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-60 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.1)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-lg transform translate-x-2 translate-y-2 -z-20 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-75\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                    className: \"h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                        children: feature.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 245,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                        children: feature.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 248,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-3\",\n                                                                        children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 261,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                        children: detail\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 262,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 260,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Smart_AI_Routing.png\",\n                                                                            alt: \"Smart AI Routing\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Enterprise_Security.png\",\n                                                                            alt: \"Enterprise Security\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Cost_Optimization.png\",\n                                                                            alt: \"Cost Optimization\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/300+_AI_Models.png\",\n                                                                            alt: \"300+ AI Models\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this);\n                                    }, \"pkXR0ykGllrTsAcXKbM4oXkC5Hs=\", false, function() {\n                                        return [\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform\n                                        ];\n                                    }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"e/ymO/RETnm/0eb4/vH+s2wwC3g=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xhbmRpbmcvRmVhdHVyZXNTZWN0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFc0Y7QUFDbEM7QUFDVTtBQWF6QjtBQUVyQyxNQUFNWSxXQUFXO0lBQ2Y7UUFDRUMsTUFBTUwsNElBQVFBO1FBQ2RNLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLFNBQVM7WUFDUDtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxlQUFlO1FBQ2ZDLGFBQWE7SUFDZjtJQUNBO1FBQ0VSLE1BQU1KLDRJQUFlQTtRQUNyQkssT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGVBQWU7UUFDZkMsYUFBYTtJQUNmO0lBQ0E7UUFDRVIsTUFBTUgsNElBQVlBO1FBQ2xCSSxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsZUFBZTtRQUNmQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFUixNQUFNRiw0SUFBWUE7UUFDbEJHLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLFNBQVM7WUFDUDtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxlQUFlO1FBQ2ZDLGFBQWE7SUFDZjtDQUNEO0FBRWMsU0FBU0M7OztJQUN0QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ21CLFNBQVNDLFdBQVcsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU1xQixhQUFhdEIsNkNBQU1BLENBQWlCO0lBRTFDRCxnREFBU0E7cUNBQUM7WUFDUixNQUFNd0I7MERBQWU7b0JBQ25CRixXQUFXRyxPQUFPSixPQUFPO2dCQUMzQjs7WUFFQUksT0FBT0MsZ0JBQWdCLENBQUMsVUFBVUY7WUFDbEM7NkNBQU8sSUFBTUMsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUg7O1FBQ3BEO29DQUFHLEVBQUU7SUFFTHhCLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU00QixXQUFXQztzREFBWTtvQkFDM0JUOzhEQUFjVSxDQUFBQSxPQUFRLENBQUNBLE9BQU8sS0FBS3RCLFNBQVN1QixNQUFNOztnQkFDcEQ7cURBQUcsT0FBTywrQkFBK0I7WUFFekM7NkNBQU8sSUFBTUMsY0FBY0o7O1FBQzdCO29DQUFHLEVBQUU7SUFFTCxxQkFDRTs7Ozs7OzBCQU1FLDhEQUFDSztnQkFBUUMsSUFBRztnQkFBc0RDLEtBQUtaOzBEQUF0QzswQkFFakMsNEVBQUNhOzhEQUFjOztzQ0FFYiw4REFBQ2pDLCtEQUFzQkE7NEJBQ3JCa0MsVUFBVTs0QkFDVkMsU0FBUzs0QkFDVEMsT0FBTTs0QkFDTkMsU0FBUTs0QkFDUkMsVUFBVTs0QkFDVkMsV0FBVTs0QkFDVkMsT0FBTztnQ0FDTEMsV0FBVyxjQUE0QixPQUFkdkIsVUFBVSxLQUFJOzRCQUN6Qzs7Ozs7O3NDQUdGLDhEQUFDZTtzRUFBYzs7OENBRWIsOERBQUNBOzhFQUFjOztzREFDYiw4REFBQ3hDLGlEQUFNQSxDQUFDaUQsRUFBRTs0Q0FDUkMsU0FBUztnREFBRVIsU0FBUztnREFBR1MsR0FBRzs0Q0FBRzs0Q0FDN0JDLGFBQWE7Z0RBQUVWLFNBQVM7Z0RBQUdTLEdBQUc7NENBQUU7NENBQ2hDRSxVQUFVO2dEQUFFQyxNQUFNOzRDQUFLOzRDQUN2QkMsWUFBWTtnREFBRUMsVUFBVTs0Q0FBSTs0Q0FDNUJWLFdBQVU7NENBQ1ZDLE9BQU87Z0RBQ0xDLFdBQVcsY0FBNkIsT0FBZnZCLFVBQVUsTUFBSzs0Q0FDMUM7O2dEQUNEOzhEQUVDLDhEQUFDZ0M7OEZBQWU7O3dEQUNiO3dEQUFJOzs7Ozs7Ozs7Ozs7O3NEQUdULDhEQUFDekQsaURBQU1BLENBQUMwRCxDQUFDOzRDQUNQUixTQUFTO2dEQUFFUixTQUFTO2dEQUFHUyxHQUFHOzRDQUFHOzRDQUM3QkMsYUFBYTtnREFBRVYsU0FBUztnREFBR1MsR0FBRzs0Q0FBRTs0Q0FDaENFLFVBQVU7Z0RBQUVDLE1BQU07NENBQUs7NENBQ3ZCQyxZQUFZO2dEQUFFQyxVQUFVO2dEQUFLRyxPQUFPOzRDQUFLOzRDQUN6Q2IsV0FBVTs0Q0FDVkMsT0FBTztnREFDTEMsV0FBVyxjQUE2QixPQUFmdkIsVUFBVSxNQUFLOzRDQUMxQztzREFDRDs7Ozs7Ozs7Ozs7OzhDQU9ILDhEQUFDZTs4RUFBYzs4Q0FDWjVCLFNBQVNnRCxHQUFHLEtBQUMsQ0FBQ0MsU0FBU0M7O3dDQUN0QixNQUFNQyxVQUFVMUQsNkNBQU1BLENBQWlCO3dDQUN2QyxNQUFNMkQsV0FBVzdELHdEQUFTQSxDQUFDNEQsU0FBUzs0Q0FBRVQsTUFBTTs0Q0FBTVcsUUFBUTt3Q0FBUTt3Q0FFbEUsZ0RBQWdEO3dDQUNoRCxNQUFNLEVBQUVDLGlCQUFpQkMsWUFBWSxFQUFFLEdBQUdsRSx5REFBU0EsQ0FBQzs0Q0FDbERtRSxRQUFRTDs0Q0FDUk0sUUFBUTtnREFBQztnREFBYTs2Q0FBWTt3Q0FDcEM7d0NBRUEsNENBQTRDO3dDQUM1QyxNQUFNQyxRQUFRcEUsNERBQVlBLENBQUNpRSxjQUFjOzRDQUFDOzRDQUFHOzRDQUFLO3lDQUFFLEVBQUU7NENBQUM7NENBQUk7NENBQUcsQ0FBQzt5Q0FBRzt3Q0FDbEUsTUFBTUksWUFBWXJFLDREQUFZQSxDQUFDaUUsY0FBYzs0Q0FBQzs0Q0FBRzs0Q0FBSzs0Q0FBSzt5Q0FBRSxFQUFFOzRDQUFDOzRDQUFNOzRDQUFHOzRDQUFHO3lDQUFLO3dDQUVqRix3Q0FBd0M7d0NBQ3hDLE1BQU1LLFdBQVd0RSw0REFBWUEsQ0FBQ2lFLGNBQWM7NENBQUM7NENBQUs7NENBQUs7eUNBQUksRUFBRTs0Q0FBQzs0Q0FBRzs0Q0FBSzt5Q0FBRTt3Q0FFeEUscUJBQ0UsOERBQUNuRSxpREFBTUEsQ0FBQ3dDLEdBQUc7NENBRVRELEtBQUt3Qjs0Q0FDTGIsU0FBUztnREFBRVIsU0FBUztnREFBR1MsR0FBRzs0Q0FBRzs0Q0FDN0JDLGFBQWE7Z0RBQUVWLFNBQVM7Z0RBQUdTLEdBQUc7NENBQUU7NENBQ2hDRSxVQUFVO2dEQUFFQyxNQUFNO2dEQUFNVyxRQUFROzRDQUFROzRDQUN4Q1YsWUFBWTtnREFDVkMsVUFBVTtnREFDVkcsT0FBT0csUUFBUTtnREFDZlcsTUFBTTs0Q0FDUjs0Q0FDQTNCLFdBQVU7NENBQ1ZDLE9BQU87Z0RBQ0wyQixpQkFBaUJaLFVBQVUsSUFBSSxZQUFZQSxVQUFVLElBQUksWUFBWUEsVUFBVSxJQUFJLFlBQVk7Z0RBQy9GWCxHQUFHbUI7Z0RBQ0hLLE9BQU9KOzRDQUNUOzs4REFHRiw4REFBQ3ZFLGlEQUFNQSxDQUFDd0MsR0FBRztvREFDVE0sV0FBVTtvREFDVkMsT0FBTzt3REFDTEwsU0FBUzhCO3dEQUNUSSxHQUFHMUUsNERBQVlBLENBQUMyRSxjQUFjOzREQUFDOzREQUFHO3lEQUFFLEVBQUU7NERBQUM7NERBQVE7eURBQU07b0RBQ3ZEOzs7Ozs7OERBSUYsOERBQUNyQzs4RkFBYzs7Ozs7OzhEQUdmLDhEQUFDQTs4RkFBYzs7Ozs7OzhEQUdmLDhEQUFDeEMsaURBQU1BLENBQUN3QyxHQUFHO29EQUNUTSxXQUFVO29EQUNWQyxPQUFPO3dEQUNMTCxTQUFTOEI7d0RBQ1RHLE9BQU96RSw0REFBWUEsQ0FBQ3NFLFVBQVU7NERBQUM7NERBQUc7eURBQUUsRUFBRTs0REFBQzs0REFBRzt5REFBRTt3REFDNUNJLEdBQUcxRSw0REFBWUEsQ0FBQzJFLGNBQWM7NERBQUM7NERBQUc7eURBQUUsRUFBRTs0REFBQzs0REFBRzt5REFBRzt3REFDN0MxQixHQUFHakQsNERBQVlBLENBQUMyRSxjQUFjOzREQUFDOzREQUFHO3lEQUFFLEVBQUU7NERBQUM7NERBQUcsQ0FBQzt5REFBRztvREFDaEQ7Ozs7Ozs4REFJRiw4REFBQ3JDO29EQUNJTyxPQUFPO3dEQUFFMkIsaUJBQWlCO29EQUFxQjs4RkFEckM7Ozs7Ozs4REFFZiw4REFBQ2xDO29EQUNJTyxPQUFPO3dEQUFFMkIsaUJBQWlCO29EQUFxQjs4RkFEckM7Ozs7Ozs4REFJZiw4REFBQ2xDOzhGQUFjOzhEQUNmLDRFQUFDQTtrR0FBYzs7MEVBRWIsOERBQUNBOzs7a0ZBQ0MsOERBQUNBO2tIQUFjOzswRkFDYiw4REFBQ0E7MEhBQWM7MEZBQ2IsNEVBQUNxQixRQUFRaEQsSUFBSTtvRkFBQ2lDLFdBQVU7Ozs7Ozs7Ozs7OzBGQUUxQiw4REFBQ047OztrR0FDQyw4REFBQ3NDO2tJQUFhO2tHQUNYakIsUUFBUS9DLEtBQUs7Ozs7OztrR0FFaEIsOERBQUM0QztrSUFBWTtrR0FDVkcsUUFBUTlDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFLdkIsOERBQUMyQztrSEFBWTtrRkFDVkcsUUFBUTdDLFdBQVc7Ozs7OztrRkFHdEIsOERBQUMrRDtrSEFBYTtrRkFDWGxCLFFBQVE1QyxPQUFPLENBQUMyQyxHQUFHLENBQUMsQ0FBQ29CLFFBQVFDLG9CQUM1Qiw4REFBQ0M7MEhBQXVCOztrR0FDdEIsOERBQUMxQztrSUFBYzs7Ozs7O2tHQUNmLDhEQUFDaUI7a0lBQWU7a0dBQTJCdUI7Ozs7Ozs7K0VBRnBDQzs7Ozs7Ozs7Ozs7Ozs7OzswRUFTZiw4REFBQ3pDOzBHQUFjOzBFQUNiLDRFQUFDQTs4R0FBYzs7c0ZBRWIsOERBQUNBO3NIQUFjOzs7Ozs7d0VBQ2RzQixVQUFVLG1CQUNULDhEQUFDcUI7NEVBQ0NDLEtBQUk7NEVBQ0pDLEtBQUk7c0hBQ007Ozs7Ozt3RUFHYnZCLFVBQVUsbUJBQ1QsOERBQUNxQjs0RUFDQ0MsS0FBSTs0RUFDSkMsS0FBSTtzSEFDTTs7Ozs7O3dFQUdidkIsVUFBVSxtQkFDVCw4REFBQ3FCOzRFQUNDQyxLQUFJOzRFQUNKQyxLQUFJO3NIQUNNOzs7Ozs7d0VBR2J2QixVQUFVLG1CQUNULDhEQUFDcUI7NEVBQ0NDLEtBQUk7NEVBQ0pDLEtBQUk7c0hBQ007Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQWhIYnZCOzs7OztvQ0F5SFg7OzRDQTFJbUIzRCxvREFBU0E7NENBR2dCRixxREFBU0E7NENBTXJDQyx3REFBWUE7NENBQ1JBLHdEQUFZQTs0Q0FHYkEsd0RBQVlBOzRDQTBCcEJBLHdEQUFZQTs0Q0FlUkEsd0RBQVlBOzRDQUNoQkEsd0RBQVlBOzRDQUNaQSx3REFBWUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXlGbkM7R0FuT3dCb0I7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxsYW5kaW5nXFxGZWF0dXJlc1NlY3Rpb24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgbW90aW9uLCB1c2VTY3JvbGwsIHVzZVRyYW5zZm9ybSwgdXNlU3ByaW5nLCB1c2VJblZpZXcgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kIGZyb20gJy4vRW5oYW5jZWRHcmlkQmFja2dyb3VuZCc7XG5pbXBvcnQge1xuICBCb2x0SWNvbixcbiAgU2hpZWxkQ2hlY2tJY29uLFxuICBDaGFydEJhckljb24sXG4gIENwdUNoaXBJY29uLFxuICBDbG9ja0ljb24sXG4gIEN1cnJlbmN5RG9sbGFySWNvbixcbiAgQ29nNlRvb3RoSWNvbixcbiAgTm9TeW1ib2xJY29uLFxuICBDb2RlQnJhY2tldEljb24sXG4gIEdsb2JlQWx0SWNvbixcbiAgTGlnaHRCdWxiSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5jb25zdCBmZWF0dXJlcyA9IFtcbiAge1xuICAgIGljb246IEJvbHRJY29uLFxuICAgIHRpdGxlOiBcIlNtYXJ0IEFJIFJvdXRpbmdcIixcbiAgICBzdWJ0aXRsZTogXCJ3aGVuIHlvdSBuZWVkIGl0XCIsXG4gICAgZGVzY3JpcHRpb246IFwiUm91S2V5IGF1dG9tYXRpY2FsbHkgZGV0ZWN0cyB5b3VyIHJlcXVlc3QgdHlwZSBhbmQgcm91dGVzIGl0IHRvIHRoZSBvcHRpbWFsIEFJIG1vZGVsLiBObyBtYW51YWwgc3dpdGNoaW5nIGJldHdlZW4gcHJvdmlkZXJzLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiSW50ZWxsaWdlbnQgcmVxdWVzdCBjbGFzc2lmaWNhdGlvbiBhbmQgcm91dGluZ1wiLFxuICAgICAgXCJBdXRvbWF0aWMgbW9kZWwgc2VsZWN0aW9uIGJhc2VkIG9uIHRhc2sgdHlwZVwiLFxuICAgICAgXCJSZWFsLXRpbWUgcGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uXCIsXG4gICAgICBcIlNlYW1sZXNzIHByb3ZpZGVyIHN3aXRjaGluZ1wiXG4gICAgXSxcbiAgICBiZ0NvbG9yOiBcImJnLWJsdWUtNjAwXCIsXG4gICAgdGV4dENvbG9yOiBcInRleHQtd2hpdGVcIixcbiAgICBzdWJ0aXRsZUNvbG9yOiBcInRleHQtYmx1ZS0xMDBcIixcbiAgICBkZXRhaWxDb2xvcjogXCJ0ZXh0LWJsdWUtNTBcIlxuICB9LFxuICB7XG4gICAgaWNvbjogU2hpZWxkQ2hlY2tJY29uLFxuICAgIHRpdGxlOiBcIkVudGVycHJpc2UgU2VjdXJpdHlcIixcbiAgICBzdWJ0aXRsZTogXCJtaWxpdGFyeS1ncmFkZSBwcm90ZWN0aW9uXCIsXG4gICAgZGVzY3JpcHRpb246IFwiTWlsaXRhcnktZ3JhZGUgQUVTLTI1Ni1HQ00gZW5jcnlwdGlvbiBmb3IgYWxsIEFQSSBrZXlzLiBZb3VyIGNyZWRlbnRpYWxzIGFyZSBzdG9yZWQgc2VjdXJlbHkgYW5kIG5ldmVyIGV4cG9zZWQuXCIsXG4gICAgZGV0YWlsczogW1xuICAgICAgXCJBRVMtMjU2LUdDTSBlbmNyeXB0aW9uIGZvciBhbGwgZGF0YVwiLFxuICAgICAgXCJaZXJvLWtub3dsZWRnZSBhcmNoaXRlY3R1cmVcIixcbiAgICAgIFwiU09DIDIgVHlwZSBJSSBjb21wbGlhbmNlXCIsXG4gICAgICBcIkFkdmFuY2VkIHRocmVhdCBkZXRlY3Rpb25cIlxuICAgIF0sXG4gICAgYmdDb2xvcjogXCJiZy1lbWVyYWxkLTYwMFwiLFxuICAgIHRleHRDb2xvcjogXCJ0ZXh0LXdoaXRlXCIsXG4gICAgc3VidGl0bGVDb2xvcjogXCJ0ZXh0LWVtZXJhbGQtMTAwXCIsXG4gICAgZGV0YWlsQ29sb3I6IFwidGV4dC1lbWVyYWxkLTUwXCJcbiAgfSxcbiAge1xuICAgIGljb246IENoYXJ0QmFySWNvbixcbiAgICB0aXRsZTogXCJDb3N0IE9wdGltaXphdGlvblwiLFxuICAgIHN1YnRpdGxlOiBcImludGVsbGlnZW50IHNwZW5kaW5nXCIsXG4gICAgZGVzY3JpcHRpb246IFwiQXV0b21hdGljIGZyZWUtdGllciBkZXRlY3Rpb24sIGJ1ZGdldCBhbGVydHMsIGFuZCBjb3N0IHRyYWNraW5nIGhlbHAgeW91IG9wdGltaXplIHNwZW5kaW5nIGFjcm9zcyBhbGwgQUkgcHJvdmlkZXJzLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiUmVhbC10aW1lIGNvc3QgdHJhY2tpbmcgYW5kIGFsZXJ0c1wiLFxuICAgICAgXCJBdXRvbWF0aWMgZnJlZS10aWVyIHV0aWxpemF0aW9uXCIsXG4gICAgICBcIkJ1ZGdldCBvcHRpbWl6YXRpb24gcmVjb21tZW5kYXRpb25zXCIsXG4gICAgICBcIk11bHRpLXByb3ZpZGVyIGNvc3QgY29tcGFyaXNvblwiXG4gICAgXSxcbiAgICBiZ0NvbG9yOiBcImJnLW9yYW5nZS02MDBcIixcbiAgICB0ZXh0Q29sb3I6IFwidGV4dC13aGl0ZVwiLFxuICAgIHN1YnRpdGxlQ29sb3I6IFwidGV4dC1vcmFuZ2UtMTAwXCIsXG4gICAgZGV0YWlsQ29sb3I6IFwidGV4dC1vcmFuZ2UtNTBcIlxuICB9LFxuICB7XG4gICAgaWNvbjogR2xvYmVBbHRJY29uLFxuICAgIHRpdGxlOiBcIjMwMCsgQUkgTW9kZWxzXCIsXG4gICAgc3VidGl0bGU6IFwidW5pZmllZCBhY2Nlc3NcIixcbiAgICBkZXNjcmlwdGlvbjogXCJBY2Nlc3MgdGhlIGxhdGVzdCBtb2RlbHMgZnJvbSBPcGVuQUksIEdvb2dsZSwgQW50aHJvcGljLCBEZWVwU2VlaywgeEFJLCBNZXRhLCBhbmQgaHVuZHJlZHMgbW9yZSB0aHJvdWdoIG9uZSB1bmlmaWVkIEFQSS5cIixcbiAgICBkZXRhaWxzOiBbXG4gICAgICBcIkNvbm5lY3QgdG8gYW55IEFJIHByb3ZpZGVyIHdpdGggb25lIEFQSVwiLFxuICAgICAgXCJBdXRvbWF0aWMgZmFpbG92ZXIgYW5kIGxvYWQgYmFsYW5jaW5nXCIsXG4gICAgICBcIlJlYWwtdGltZSBwZXJmb3JtYW5jZSBtb25pdG9yaW5nXCIsXG4gICAgICBcIkdsb2JhbCBpbmZyYXN0cnVjdHVyZSBkZXBsb3ltZW50XCJcbiAgICBdLFxuICAgIGJnQ29sb3I6IFwiYmctcHVycGxlLTYwMFwiLFxuICAgIHRleHRDb2xvcjogXCJ0ZXh0LXdoaXRlXCIsXG4gICAgc3VidGl0bGVDb2xvcjogXCJ0ZXh0LXB1cnBsZS0xMDBcIixcbiAgICBkZXRhaWxDb2xvcjogXCJ0ZXh0LXB1cnBsZS01MFwiXG4gIH1cbl07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZlYXR1cmVzU2VjdGlvbigpIHtcbiAgY29uc3QgW2FjdGl2ZUNhcmQsIHNldEFjdGl2ZUNhcmRdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtzY3JvbGxZLCBzZXRTY3JvbGxZXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBzZWN0aW9uUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcbiAgICAgIHNldFNjcm9sbFkod2luZG93LnNjcm9sbFkpO1xuICAgIH07XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgaGFuZGxlU2Nyb2xsKTtcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbCk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0QWN0aXZlQ2FyZChwcmV2ID0+IChwcmV2ICsgMSkgJSBmZWF0dXJlcy5sZW5ndGgpO1xuICAgIH0sIDQwMDApOyAvLyBBdXRvLWFkdmFuY2UgZXZlcnkgNCBzZWNvbmRzXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIC5wZXJzcGVjdGl2ZS0xMDAwIHtcbiAgICAgICAgICBwZXJzcGVjdGl2ZTogMTAwMHB4O1xuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG4gICAgICA8c2VjdGlvbiBpZD1cImZlYXR1cmVzXCIgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuIHB5LTIwXCIgcmVmPXtzZWN0aW9uUmVmfT5cbiAgICAgIHsvKiBCYWNrZ3JvdW5kIHdpdGggUm91S2V5IGNvbG9ycyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1bIzA0MDcxNl0gdG8tWyMxQzA1MUNdIHJlbGF0aXZlXCI+XG4gICAgICAgIHsvKiBFbmhhbmNlZCBHcmlkIEJhY2tncm91bmQgd2l0aCBQYXJhbGxheCAqL31cbiAgICAgICAgPEVuaGFuY2VkR3JpZEJhY2tncm91bmRcbiAgICAgICAgICBncmlkU2l6ZT17NDV9XG4gICAgICAgICAgb3BhY2l0eT17MC4wNn1cbiAgICAgICAgICBjb2xvcj1cIiNmZjZiMzVcIlxuICAgICAgICAgIHZhcmlhbnQ9XCJwcmVtaXVtXCJcbiAgICAgICAgICBhbmltYXRlZD17dHJ1ZX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCJcbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgdHJhbnNmb3JtOiBgdHJhbnNsYXRlWSgke3Njcm9sbFkgKiAwLjF9cHgpYFxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCByZWxhdGl2ZVwiPlxuICAgICAgICAgIHsvKiBTZWN0aW9uIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTUgfX1cbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBzbTp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yIGxlYWRpbmctdGlnaHRcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogYHRyYW5zbGF0ZVkoJHtzY3JvbGxZICogMC4wNX1weClgXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEVudGVycHJpc2UtR3JhZGVcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC10cmFuc3BhcmVudCBiZy1jbGlwLXRleHQgYmctZ3JhZGllbnQtdG8tciBmcm9tLVsjZmY2YjM1XSB0by1bI2Y3OTMxZV1cIj5cbiAgICAgICAgICAgICAgICB7JyAnfUFJIEluZnJhc3RydWN0dXJlXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvbW90aW9uLmgyPlxuICAgICAgICAgICAgPG1vdGlvbi5wXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTUgfX1cbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZGVsYXk6IDAuMDUgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktMzAwIG1heC13LTN4bCBteC1hdXRvIGxlYWRpbmctcmVsYXhlZCBtYi00XCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGB0cmFuc2xhdGVZKCR7c2Nyb2xsWSAqIDAuMDN9cHgpYFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBSb3VLZXkgcHJvdmlkZXMgbWlsaXRhcnktZ3JhZGUgc2VjdXJpdHksIGludGVsbGlnZW50IHJvdXRpbmcsIGFuZCBjb21wcmVoZW5zaXZlIGFuYWx5dGljc1xuICAgICAgICAgICAgICBmb3IgdGhlIG1vc3QgZGVtYW5kaW5nIEFJIHdvcmtsb2Fkcy4gQnVpbHQgZm9yIHNjYWxlLCBkZXNpZ25lZCBmb3IgcGVyZm9ybWFuY2UuXG4gICAgICAgICAgICA8L21vdGlvbi5wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZlYXR1cmUgQ2FyZHMgLSBFbmhhbmNlZCB3aXRoIEFkdmFuY2VkIFNjcm9sbCBFZmZlY3RzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xMlwiPlxuICAgICAgICAgICAge2ZlYXR1cmVzLm1hcCgoZmVhdHVyZSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgY2FyZFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gICAgICAgICAgICAgIGNvbnN0IGlzSW5WaWV3ID0gdXNlSW5WaWV3KGNhcmRSZWYsIHsgb25jZTogdHJ1ZSwgbWFyZ2luOiBcIi01MHB4XCIgfSk7XG5cbiAgICAgICAgICAgICAgLy8gU2ltcGxpZmllZCBzY3JvbGwgdHJhY2tpbmcgLSBvbmx5IHdoZW4gbmVlZGVkXG4gICAgICAgICAgICAgIGNvbnN0IHsgc2Nyb2xsWVByb2dyZXNzOiBjYXJkUHJvZ3Jlc3MgfSA9IHVzZVNjcm9sbCh7XG4gICAgICAgICAgICAgICAgdGFyZ2V0OiBjYXJkUmVmLFxuICAgICAgICAgICAgICAgIG9mZnNldDogW1wic3RhcnQgZW5kXCIsIFwiZW5kIHN0YXJ0XCJdXG4gICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgIC8vIFJlZHVjZWQgdHJhbnNmb3JtcyBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlXG4gICAgICAgICAgICAgIGNvbnN0IGNhcmRZID0gdXNlVHJhbnNmb3JtKGNhcmRQcm9ncmVzcywgWzAsIDAuNSwgMV0sIFsyMCwgMCwgLTIwXSk7XG4gICAgICAgICAgICAgIGNvbnN0IGNhcmRTY2FsZSA9IHVzZVRyYW5zZm9ybShjYXJkUHJvZ3Jlc3MsIFswLCAwLjMsIDAuNywgMV0sIFswLjk1LCAxLCAxLCAwLjk1XSk7XG5cbiAgICAgICAgICAgICAgLy8gU2ltcGxlIHdhdmUgZWZmZWN0IC0gbGVzcyBjb21wdXRhdGlvblxuICAgICAgICAgICAgICBjb25zdCB3YXZlR2xvdyA9IHVzZVRyYW5zZm9ybShjYXJkUHJvZ3Jlc3MsIFswLjMsIDAuNSwgMC43XSwgWzAsIDAuNSwgMF0pO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICByZWY9e2NhcmRSZWZ9XG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDUwIH19XG4gICAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlLCBtYXJnaW46IFwiLTUwcHhcIiB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17e1xuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMC42LFxuICAgICAgICAgICAgICAgICAgICBkZWxheTogaW5kZXggKiAwLjEsXG4gICAgICAgICAgICAgICAgICAgIGVhc2U6IFwiZWFzZU91dFwiXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgcm91bmRlZC0zeGwgcC04IHNoYWRvdy0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBob3Zlcjpib3JkZXItd2hpdGUvMzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLVsxLjAxXSBob3ZlcjotdHJhbnNsYXRlLXktMSBjdXJzb3ItcG9pbnRlciBnbG9zc3ktc2hpbmUgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogaW5kZXggPT09IDAgPyAnIzI1NjNlYicgOiBpbmRleCA9PT0gMSA/ICcjMDU5NjY5JyA6IGluZGV4ID09PSAyID8gJyNlYTU4MGMnIDogJyM5MzMzZWEnLFxuICAgICAgICAgICAgICAgICAgICB5OiBjYXJkWSxcbiAgICAgICAgICAgICAgICAgICAgc2NhbGU6IGNhcmRTY2FsZVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgey8qIFNjcm9sbC10cmlnZ2VyZWQgd2F2ZSBlZmZlY3QgKi99XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC0zeGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8yMCB0by10cmFuc3BhcmVudCBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IHdhdmVHbG93LFxuICAgICAgICAgICAgICAgICAgICB4OiB1c2VUcmFuc2Zvcm0od2F2ZVByb2dyZXNzLCBbMCwgMV0sIFtcIi01MCVcIiwgXCI1MCVcIl0pXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7LyogR2xvc3N5IHNoaW5lIGVmZmVjdCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC0zeGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS13aGl0ZS8zMCB2aWEtd2hpdGUvMTAgdG8tdHJhbnNwYXJlbnQgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi03MDAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XG5cbiAgICAgICAgICAgICAgICB7LyogRW5oYW5jZWQgZ2xvdyBlZmZlY3Qgb24gaG92ZXIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtM3hsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS10cmFuc3BhcmVudCB2aWEtd2hpdGUvNSB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTUwMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgLz5cblxuICAgICAgICAgICAgICAgIHsvKiBTY3JvbGwtYmFzZWQgcGFydGljbGUgdHJhaWwgKi99XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IHJpZ2h0LTQgdy0yIGgtMiBiZy1vcmFuZ2UtNDAwIHJvdW5kZWQtZnVsbCBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IHdhdmVHbG93LFxuICAgICAgICAgICAgICAgICAgICBzY2FsZTogdXNlVHJhbnNmb3JtKHdhdmVHbG93LCBbMCwgMV0sIFsxLCAyXSksXG4gICAgICAgICAgICAgICAgICAgIHg6IHVzZVRyYW5zZm9ybSh3YXZlUHJvZ3Jlc3MsIFswLCAxXSwgWzAsIDIwXSksXG4gICAgICAgICAgICAgICAgICAgIHk6IHVzZVRyYW5zZm9ybSh3YXZlUHJvZ3Jlc3MsIFswLCAxXSwgWzAsIC0xMF0pXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7LyogU3RhY2tpbmcgc2hhZG93IGVmZmVjdHMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtM3hsIHNoYWRvdy14bCB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtMSB0cmFuc2xhdGUteS0xIC16LTEwIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTYwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuMiknIH19IC8+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtM3hsIHNoYWRvdy1sZyB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtMiB0cmFuc2xhdGUteS0yIC16LTIwIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTQwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBkZWxheS03NVwiXG4gICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuMSknIH19IC8+XG5cbiAgICAgICAgICAgICAgICB7LyogQ2FyZCBjb250ZW50IHdyYXBwZXIgd2l0aCBzdWJ0bGUgM0QgZmxpcCBlZmZlY3QgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB0cmFuc2Zvcm0gdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnJvdGF0ZS15LTIgYmFja2ZhY2UtaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC0xMiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBMZWZ0IFNpZGUgLSBDb250ZW50ICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctYmxhY2svMTAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBncm91cC1ob3ZlcjpiZy1ibGFjay8yMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIGdyb3VwLWhvdmVyOnJvdGF0ZS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZmVhdHVyZS5pY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC13aGl0ZSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmUudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXdoaXRlLzcwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLnN1YnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtd2hpdGUvOTAgbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS5kZXRhaWxzLm1hcCgoZGV0YWlsLCBpZHgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2lkeH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMyB0ZXh0LXdoaXRlLzgwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xLjUgaC0xLjUgYmctd2hpdGUvNjAgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTAgbXQtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy1yZWxheGVkXCI+e2RldGFpbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBSaWdodCBTaWRlIC0gVmlzdWFsIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGFzcGVjdC1bMy8yXSBiZy1ibGFjay8yMCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlbiBncm91cC1ob3Zlcjpib3JkZXItd2hpdGUvMzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnNoYWRvdy0yeGwgZ3JvdXAtaG92ZXI6c2NhbGUtWzEuMDJdIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIElubmVyIGdsb3cgZWZmZWN0IGZvciBpbWFnZSBjb250YWluZXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20td2hpdGUvMTAgdmlhLXRyYW5zcGFyZW50IHRvLXRyYW5zcGFyZW50IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tNTAwIHJvdW5kZWQtMnhsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7aW5kZXggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9XCIvU21hcnRfQUlfUm91dGluZy5wbmdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJTbWFydCBBSSBSb3V0aW5nXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgcm91bmRlZC0yeGwgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSByZWxhdGl2ZSB6LTEwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICB7aW5kZXggPT09IDEgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9XCIvRW50ZXJwcmlzZV9TZWN1cml0eS5wbmdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJFbnRlcnByaXNlIFNlY3VyaXR5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgcm91bmRlZC0yeGwgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSByZWxhdGl2ZSB6LTEwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICB7aW5kZXggPT09IDIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9XCIvQ29zdF9PcHRpbWl6YXRpb24ucG5nXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiQ29zdCBPcHRpbWl6YXRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciByb3VuZGVkLTJ4bCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi01MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHJlbGF0aXZlIHotMTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT0gMyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz1cIi8zMDArX0FJX01vZGVscy5wbmdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCIzMDArIEFJIE1vZGVsc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIHJvdW5kZWQtMnhsIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTUwMCBncm91cC1ob3ZlcjpzY2FsZS0xMDUgcmVsYXRpdmUgei0xMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJ1c2VTY3JvbGwiLCJ1c2VUcmFuc2Zvcm0iLCJ1c2VJblZpZXciLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIkVuaGFuY2VkR3JpZEJhY2tncm91bmQiLCJCb2x0SWNvbiIsIlNoaWVsZENoZWNrSWNvbiIsIkNoYXJ0QmFySWNvbiIsIkdsb2JlQWx0SWNvbiIsImZlYXR1cmVzIiwiaWNvbiIsInRpdGxlIiwic3VidGl0bGUiLCJkZXNjcmlwdGlvbiIsImRldGFpbHMiLCJiZ0NvbG9yIiwidGV4dENvbG9yIiwic3VidGl0bGVDb2xvciIsImRldGFpbENvbG9yIiwiRmVhdHVyZXNTZWN0aW9uIiwiYWN0aXZlQ2FyZCIsInNldEFjdGl2ZUNhcmQiLCJzY3JvbGxZIiwic2V0U2Nyb2xsWSIsInNlY3Rpb25SZWYiLCJoYW5kbGVTY3JvbGwiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwibGVuZ3RoIiwiY2xlYXJJbnRlcnZhbCIsInNlY3Rpb24iLCJpZCIsInJlZiIsImRpdiIsImdyaWRTaXplIiwib3BhY2l0eSIsImNvbG9yIiwidmFyaWFudCIsImFuaW1hdGVkIiwiY2xhc3NOYW1lIiwic3R5bGUiLCJ0cmFuc2Zvcm0iLCJoMiIsImluaXRpYWwiLCJ5Iiwid2hpbGVJblZpZXciLCJ2aWV3cG9ydCIsIm9uY2UiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJzcGFuIiwicCIsImRlbGF5IiwibWFwIiwiZmVhdHVyZSIsImluZGV4IiwiY2FyZFJlZiIsImlzSW5WaWV3IiwibWFyZ2luIiwic2Nyb2xsWVByb2dyZXNzIiwiY2FyZFByb2dyZXNzIiwidGFyZ2V0Iiwib2Zmc2V0IiwiY2FyZFkiLCJjYXJkU2NhbGUiLCJ3YXZlR2xvdyIsImVhc2UiLCJiYWNrZ3JvdW5kQ29sb3IiLCJzY2FsZSIsIngiLCJ3YXZlUHJvZ3Jlc3MiLCJoMyIsInVsIiwiZGV0YWlsIiwiaWR4IiwibGkiLCJpbWciLCJzcmMiLCJhbHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});