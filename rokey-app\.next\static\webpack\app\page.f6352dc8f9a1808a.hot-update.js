"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    var _s1 = $RefreshSig$();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1fa32eff8cf0f851\",\n                children: \".perspective-1000.jsx-1fa32eff8cf0f851{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-12\",\n                                    children: features.map(_s1((feature, index)=>{\n                                        _s1();\n                                        const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n                                        const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView)(cardRef, {\n                                            once: true,\n                                            margin: \"-50px\"\n                                        });\n                                        // Simplified scroll tracking - only when needed\n                                        const { scrollYProgress: cardProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n                                            target: cardRef,\n                                            offset: [\n                                                \"start end\",\n                                                \"end start\"\n                                            ]\n                                        });\n                                        // Reduced transforms for better performance\n                                        const cardY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            20,\n                                            0,\n                                            -20\n                                        ]);\n                                        const cardScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0,\n                                            0.3,\n                                            0.7,\n                                            1\n                                        ], [\n                                            0.95,\n                                            1,\n                                            1,\n                                            0.95\n                                        ]);\n                                        // Simple wave effect - less computation\n                                        const waveGlow = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform)(cardProgress, [\n                                            0.3,\n                                            0.5,\n                                            0.7\n                                        ], [\n                                            0,\n                                            0.5,\n                                            0\n                                        ]);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: cardRef,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true,\n                                                margin: \"-50px\"\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1,\n                                                ease: \"easeOut\"\n                                            },\n                                            className: \"group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-300 hover:scale-[1.01] hover:-translate-y-1 cursor-pointer glossy-shine overflow-hidden\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                y: cardY,\n                                                scale: cardScale\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/20 via-transparent to-transparent pointer-events-none\",\n                                                    style: {\n                                                        opacity: waveGlow\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/20 via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.15)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-lg transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-50 transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                    className: \"h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 224,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                        children: feature.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 228,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                        children: feature.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 231,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-3\",\n                                                                        children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 244,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                        children: detail\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 245,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Smart_AI_Routing.png\",\n                                                                            alt: \"Smart AI Routing\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Enterprise_Security.png\",\n                                                                            alt: \"Enterprise Security\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Cost_Optimization.png\",\n                                                                            alt: \"Cost Optimization\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/300+_AI_Models.png\",\n                                                                            alt: \"300+ AI Models\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this);\n                                    }, \"vrHvaBRRhN8YFA+R4tb8/lUT3Hg=\", false, function() {\n                                        return [\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useTransform\n                                        ];\n                                    }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"e/ymO/RETnm/0eb4/vH+s2wwC3g=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});