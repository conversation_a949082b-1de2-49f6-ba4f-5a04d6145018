"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    var _s1 = $RefreshSig$();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1fa32eff8cf0f851\",\n                children: \".perspective-1000.jsx-1fa32eff8cf0f851{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-12\",\n                                    children: features.map(_s1((feature, index)=>{\n                                        _s1();\n                                        const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n                                        const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView)(cardRef, {\n                                            once: false,\n                                            margin: \"-100px\"\n                                        });\n                                        // Individual card scroll tracking\n                                        const { scrollYProgress: cardProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll)({\n                                            target: cardRef,\n                                            offset: [\n                                                \"start end\",\n                                                \"end start\"\n                                            ]\n                                        });\n                                        // Smooth spring for card animations\n                                        const cardSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring)(cardProgress, {\n                                            stiffness: 100,\n                                            damping: 30\n                                        });\n                                        // Advanced scroll transforms for each card\n                                        const cardY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            50,\n                                            0,\n                                            -50\n                                        ]);\n                                        const cardRotateX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            5,\n                                            0,\n                                            -5\n                                        ]);\n                                        const cardRotateY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            -2,\n                                            0,\n                                            2\n                                        ]);\n                                        const cardScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.3,\n                                            0.7,\n                                            1\n                                        ], [\n                                            0.8,\n                                            1,\n                                            1,\n                                            0.9\n                                        ]);\n                                        const cardOpacity = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.2,\n                                            0.8,\n                                            1\n                                        ], [\n                                            0.3,\n                                            1,\n                                            1,\n                                            0.3\n                                        ]);\n                                        // Magnetic scroll effect - cards slightly follow scroll direction\n                                        const magneticY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(scrollY, [\n                                            0,\n                                            1000,\n                                            2000,\n                                            3000\n                                        ], [\n                                            0,\n                                            index % 2 === 0 ? -10 : 10,\n                                            index % 2 === 0 ? 10 : -10,\n                                            0\n                                        ]);\n                                        // Wave effect that travels through cards\n                                        const waveDelay = index * 0.1;\n                                        const waveProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            0,\n                                            1,\n                                            0\n                                        ]);\n                                        const waveScale = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(waveProgress, [\n                                            0,\n                                            1\n                                        ], [\n                                            1,\n                                            1.05\n                                        ]);\n                                        const waveGlow = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(waveProgress, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            0,\n                                            1,\n                                            0\n                                        ]);\n                                        // Scroll-based tilt effect\n                                        const tiltX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            3,\n                                            0,\n                                            -3\n                                        ]);\n                                        const tiltY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(cardSpring, [\n                                            0,\n                                            0.5,\n                                            1\n                                        ], [\n                                            index % 2 === 0 ? -2 : 2,\n                                            0,\n                                            index % 2 === 0 ? 2 : -2\n                                        ]);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: cardRef,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 100,\n                                                rotateX: 10\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0,\n                                                rotateX: 0\n                                            },\n                                            viewport: {\n                                                once: false,\n                                                margin: \"-50px\"\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.15,\n                                                type: \"spring\",\n                                                stiffness: 100,\n                                                damping: 20\n                                            },\n                                            className: \"group relative rounded-3xl p-8 shadow-2xl border border-white/10 hover:border-white/30 transition-all duration-500 transform-gpu perspective-1000 hover:scale-[1.02] hover:-translate-y-2 cursor-pointer glossy-shine glossy-sweep card-stack card-flip depth-shadow glow-pulse overflow-hidden\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transformStyle: 'preserve-3d',\n                                                y: cardY,\n                                                rotateX: cardRotateX,\n                                                rotateY: cardRotateY,\n                                                scale: cardScale,\n                                                opacity: cardOpacity,\n                                                transform: \"translateY(\".concat(magneticY, \"px)\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.2)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-xl transform translate-x-1 translate-y-1 -z-10 opacity-0 group-hover:opacity-60 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: 'rgba(0, 0, 0, 0.1)'\n                                                    },\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 rounded-3xl shadow-lg transform translate-x-2 translate-y-2 -z-20 opacity-0 group-hover:opacity-40 transition-all duration-500 delay-75\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"relative transform transition-transform duration-500 group-hover:rotate-y-2 backface-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center group-hover:bg-black/20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                    className: \"h-6 w-6 text-white transition-transform duration-300 group-hover:scale-110\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                        children: feature.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 251,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                        children: feature.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 254,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 250,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"space-y-3\",\n                                                                        children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                        children: detail\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                        lineNumber: 268,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden group-hover:border-white/30 transition-all duration-500 group-hover:shadow-2xl group-hover:scale-[1.02] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Smart_AI_Routing.png\",\n                                                                            alt: \"Smart AI Routing\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Enterprise_Security.png\",\n                                                                            alt: \"Enterprise Security\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/Cost_Optimization.png\",\n                                                                            alt: \"Cost Optimization\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/300+_AI_Models.png\",\n                                                                            alt: \"300+ AI Models\",\n                                                                            className: \"jsx-1fa32eff8cf0f851\" + \" \" + \"w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover:scale-105 relative z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this);\n                                    }, \"bhgWYbsCcX+7LFQa1UQqaIAzXFo=\", false, function() {\n                                        return [\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_9__.useInView,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_10__.useScroll,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_11__.useSpring,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n                                            framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n                                        ];\n                                    }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"e/ymO/RETnm/0eb4/vH+s2wwC3g=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});