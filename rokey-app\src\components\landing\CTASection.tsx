'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';

export default function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/5 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />

      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* N8N Style Container */}
        <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 rounded-3xl p-12 border border-white/10 backdrop-blur-sm">
          <div className="text-center">
            {/* Main CTA */}
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight"
            >
              There's nothing you
              <br />can't automate with RouKey.
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="mb-8"
            >
              <p className="text-lg text-gray-300 mb-2">
                Our customer's words, not ours.
              </p>
              <p className="text-lg text-gray-300">
                Skeptical? <span className="text-[#ff6b35] hover:text-[#f7931e] transition-colors cursor-pointer">Try it out</span>, and see for yourself.
              </p>
            </motion.div>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="mb-0"
            >
              <InstantLink
                href="/auth/signup?plan=professional"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105 transform"
              >
                Start building
              </InstantLink>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
