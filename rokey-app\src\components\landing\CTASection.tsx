'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';
import { useState, useRef } from 'react';

export default function CTASection() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/5 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />

      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Glossy Container with Mouse Tracking */}
        <div
          ref={containerRef}
          onMouseMove={handleMouseMove}
          className="relative bg-gradient-to-br from-gray-800/40 to-gray-900/60 rounded-3xl p-12 border border-white/10 backdrop-blur-sm overflow-hidden group"
          style={{
            background: `
              radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px,
                rgba(255, 255, 255, 0.06),
                transparent 40%),
              linear-gradient(135deg, rgba(107, 114, 128, 0.4), rgba(17, 24, 39, 0.6))
            `
          }}
        >
          {/* Glossy overlay */}
          <div
            className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
            style={{
              background: `radial-gradient(300px circle at ${mousePosition.x}px ${mousePosition.y}px,
                rgba(255, 255, 255, 0.1),
                transparent 50%)`
            }}
          />

          <div className="text-center relative z-10">
            {/* Main CTA */}
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight"
            >
              Scale your AI infrastructure
              <br />without the complexity.
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="mb-8"
            >
              <p className="text-lg text-gray-300 mb-2">
                300+ AI models, intelligent routing, zero vendor lock-in.
              </p>
              <p className="text-lg text-gray-300">
                Ready to transform your AI workflow? <span className="text-[#ff6b35] hover:text-[#f7931e] transition-colors cursor-pointer">Start building</span> in minutes.
              </p>
            </motion.div>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="mb-0"
            >
              <InstantLink
                href="/auth/signup?plan=professional"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105 transform relative overflow-hidden group"
              >
                {/* Glossy overlay for button */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <span className="relative z-10">Start building</span>
              </InstantLink>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
