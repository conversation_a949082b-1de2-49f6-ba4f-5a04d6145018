"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [cardVisibility, setCardVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    console.log('Scroll event triggered, scrollY:', window.scrollY);\n                    setScrollY(window.scrollY);\n                    // Calculate card visibility based on scroll position\n                    console.log('Card refs length:', cardRefs.current.length);\n                    console.log('Card refs:', cardRefs.current.map({\n                        \"FeaturesSection.useEffect.handleScroll\": (ref, i)=>\"Card \".concat(i, \": \").concat(ref ? 'exists' : 'null')\n                    }[\"FeaturesSection.useEffect.handleScroll\"]));\n                    if (cardRefs.current.length > 0 && cardRefs.current.some({\n                        \"FeaturesSection.useEffect.handleScroll\": (ref)=>ref !== null\n                    }[\"FeaturesSection.useEffect.handleScroll\"])) {\n                        const viewportHeight = window.innerHeight;\n                        const viewportTop = window.scrollY;\n                        const viewportBottom = viewportTop + viewportHeight;\n                        const viewportCenter = viewportTop + viewportHeight / 2;\n                        console.log('Viewport info:', {\n                            viewportHeight,\n                            viewportTop,\n                            viewportBottom,\n                            viewportCenter\n                        });\n                        const visibilityScores = cardRefs.current.map({\n                            \"FeaturesSection.useEffect.handleScroll.visibilityScores\": (cardRef, index)=>{\n                                if (!cardRef) {\n                                    console.log(\"Card \".concat(index, \": ref is null\"));\n                                    return 0;\n                                }\n                                const rect = cardRef.getBoundingClientRect();\n                                const cardTop = rect.top + window.scrollY;\n                                const cardBottom = cardTop + rect.height;\n                                const cardCenter = cardTop + rect.height / 2;\n                                console.log(\"Card \".concat(index, \":\"), {\n                                    top: cardTop,\n                                    bottom: cardBottom,\n                                    center: cardCenter,\n                                    height: rect.height\n                                });\n                                // Check if card is in viewport\n                                const isInViewport = cardBottom > viewportTop && cardTop < viewportBottom;\n                                if (!isInViewport) {\n                                    console.log(\"Card \".concat(index, \": not in viewport\"));\n                                    return 0;\n                                }\n                                // Calculate how much of the card is visible\n                                const visibleTop = Math.max(cardTop, viewportTop);\n                                const visibleBottom = Math.min(cardBottom, viewportBottom);\n                                const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n                                const visibilityRatio = visibleHeight / rect.height;\n                                // Calculate distance from viewport center for focus effect\n                                const distanceFromCenter = Math.abs(cardCenter - viewportCenter);\n                                const maxDistance = viewportHeight / 2;\n                                const centerProximity = Math.max(0, 1 - distanceFromCenter / maxDistance);\n                                // Combine visibility ratio and center proximity\n                                const finalScore = visibilityRatio * 0.3 + centerProximity * 0.7;\n                                console.log(\"Card \".concat(index, \": visibilityRatio=\").concat(visibilityRatio.toFixed(2), \", centerProximity=\").concat(centerProximity.toFixed(2), \", finalScore=\").concat(finalScore.toFixed(2)));\n                                return finalScore;\n                            }\n                        }[\"FeaturesSection.useEffect.handleScroll.visibilityScores\"]);\n                        console.log('Setting card visibility:', visibilityScores);\n                        setCardVisibility(visibilityScores);\n                    } else {\n                        console.log('No card refs available yet');\n                    }\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            console.log('Setting up scroll listener');\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            window.addEventListener('resize', handleScroll, {\n                passive: true\n            });\n            // Delay initial calculation to ensure refs are set\n            setTimeout(handleScroll, 100);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>{\n                    console.log('Cleaning up scroll listener');\n                    window.removeEventListener('scroll', handleScroll);\n                    window.removeEventListener('resize', handleScroll);\n                }\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"79d68e2849b71ee5\",\n                children: '.perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.glossy-card{position:relative;overflow:hidden;-webkit-transition:all.5s cubic-bezier(.4,0,.2,1);-moz-transition:all.5s cubic-bezier(.4,0,.2,1);-o-transition:all.5s cubic-bezier(.4,0,.2,1);transition:all.5s cubic-bezier(.4,0,.2,1)}.glossy-card::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.2),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);-webkit-transition:left.6s ease;-moz-transition:left.6s ease;-o-transition:left.6s ease;transition:left.6s ease;z-index:1;pointer-events:none}.glossy-card:hover::before{left:100%}.glossy-card::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-moz-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:-o-linear-gradient(315deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);background:linear-gradient(135deg,rgba(255,255,255,.15)0%,transparent 50%,rgba(255,255,255,.08)100%);opacity:0;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;z-index:1;pointer-events:none}.glossy-card:hover::after{opacity:1}.glossy-card:hover{-webkit-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;-moz-box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2)!important;border-color:rgba(255,255,255,.3)!important}.card-content{position:relative;z-index:2}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-12\",\n                                    children: features.map((feature, index)=>{\n                                        const visibility = cardVisibility[index] || 0;\n                                        const scale = 0.75 + visibility * 0.25; // Scale from 0.75 to 1.0 (more pronounced)\n                                        const cardOpacity = 0.3 + visibility * 0.7; // Opacity from 0.3 to 1.0 (more pronounced)\n                                        // Debug: Log the applied values\n                                        console.log(\"Card \".concat(index, \" render:\"), {\n                                            visibility,\n                                            scale,\n                                            cardOpacity\n                                        });\n                                        // Enhanced glow for most visible card\n                                        const glowIntensity = visibility > 0.7 ? visibility : 0;\n                                        const baseColor = index === 0 ? '37, 99, 235' : index === 1 ? '5, 150, 105' : index === 2 ? '234, 88, 12' : '147, 51, 234';\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: (el)=>{\n                                                cardRefs.current[index] = el;\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"glossy-card rounded-3xl p-8 shadow-2xl border border-white/10\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transform: \"scale(\".concat(scale, \")\"),\n                                                opacity: cardOpacity,\n                                                boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25),\\n                               0 0 0 1px rgba(255, 255, 255, 0.1),\\n                               inset 0 1px 0 rgba(255, 255, 255, 0.1),\\n                               0 0 \".concat(20 + glowIntensity * 30, \"px rgba(\").concat(baseColor, \", \").concat(glowIntensity * 0.4, \")\"),\n                                                borderColor: \"rgba(255, 255, 255, \".concat(0.1 + glowIntensity * 0.2, \")\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                className: \"h-6 w-6 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                    children: feature.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                    children: feature.subtitle\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"space-y-3\",\n                                                                    children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 365,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-79d68e2849b71ee5\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                    children: detail\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 366,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-79d68e2849b71ee5\" + \" \" + \"flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Smart_AI_Routing.png\",\n                                                                        alt: \"Smart AI Routing\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Enterprise_Security.png\",\n                                                                        alt: \"Enterprise Security\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Cost_Optimization.png\",\n                                                                        alt: \"Cost Optimization\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/300+_AI_Models.png\",\n                                                                        alt: \"300+ AI Models\",\n                                                                        className: \"jsx-79d68e2849b71ee5\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"LyzPP6iq6w4fKeBfhPlXvuCGEpA=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});