"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,GlobeAltIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Intelligent request classification and routing\",\n            \"Automatic model selection based on task type\",\n            \"Real-time performance optimization\",\n            \"Seamless provider switching\"\n        ],\n        bgColor: \"bg-blue-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-blue-100\",\n        detailColor: \"text-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"military-grade protection\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"AES-256-GCM encryption for all data\",\n            \"Zero-knowledge architecture\",\n            \"SOC 2 Type II compliance\",\n            \"Advanced threat detection\"\n        ],\n        bgColor: \"bg-emerald-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-emerald-100\",\n        detailColor: \"text-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"intelligent spending\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Real-time cost tracking and alerts\",\n            \"Automatic free-tier utilization\",\n            \"Budget optimization recommendations\",\n            \"Multi-provider cost comparison\"\n        ],\n        bgColor: \"bg-orange-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-orange-100\",\n        detailColor: \"text-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_GlobeAltIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Global infrastructure deployment\"\n        ],\n        bgColor: \"bg-purple-600\",\n        textColor: \"text-white\",\n        subtitleColor: \"text-purple-100\",\n        detailColor: \"text-purple-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [activeCard, setActiveCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturesSection.useEffect.interval\": ()=>{\n                    setActiveCard({\n                        \"FeaturesSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturesSection.useEffect.interval\"]);\n                }\n            }[\"FeaturesSection.useEffect.interval\"], 4000); // Auto-advance every 4 seconds\n            return ({\n                \"FeaturesSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d51bc32227e60de8\",\n                children: '.perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.glossy-card{position:relative;overflow:hidden;-webkit-transition:all.4s cubic-bezier(.4,0,.2,1);-moz-transition:all.4s cubic-bezier(.4,0,.2,1);-o-transition:all.4s cubic-bezier(.4,0,.2,1);transition:all.4s cubic-bezier(.4,0,.2,1);-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;transform-style:preserve-3d}.glossy-card::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.3),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.3),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.3),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);-webkit-transition:left.8s ease;-moz-transition:left.8s ease;-o-transition:left.8s ease;transition:left.8s ease;z-index:1;pointer-events:none}.glossy-card:hover::before{left:100%}.glossy-card::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(255,255,255,.2)0%,transparent 50%,rgba(255,255,255,.1)100%);background:-moz-linear-gradient(315deg,rgba(255,255,255,.2)0%,transparent 50%,rgba(255,255,255,.1)100%);background:-o-linear-gradient(315deg,rgba(255,255,255,.2)0%,transparent 50%,rgba(255,255,255,.1)100%);background:linear-gradient(135deg,rgba(255,255,255,.2)0%,transparent 50%,rgba(255,255,255,.1)100%);opacity:0;-webkit-transition:opacity.4s ease;-moz-transition:opacity.4s ease;-o-transition:opacity.4s ease;transition:opacity.4s ease;z-index:1;pointer-events:none}.glossy-card:hover::after{opacity:1}.glossy-card:hover{-webkit-transform:translatey(-8px)rotatex(2deg)rotatey(2deg)scale(1.02);-moz-transform:translatey(-8px)rotatex(2deg)rotatey(2deg)scale(1.02);-ms-transform:translatey(-8px)rotatex(2deg)rotatey(2deg)scale(1.02);-o-transform:translatey(-8px)rotatex(2deg)rotatey(2deg)scale(1.02);transform:translatey(-8px)rotatex(2deg)rotatey(2deg)scale(1.02);-webkit-box-shadow:0 40px 80px -12px rgba(0,0,0,.5),0 0 0 1px rgba(255,255,255,.3),inset 0 1px 0 rgba(255,255,255,.3),0 0 40px rgba(255,107,53,.2)!important;-moz-box-shadow:0 40px 80px -12px rgba(0,0,0,.5),0 0 0 1px rgba(255,255,255,.3),inset 0 1px 0 rgba(255,255,255,.3),0 0 40px rgba(255,107,53,.2)!important;box-shadow:0 40px 80px -12px rgba(0,0,0,.5),0 0 0 1px rgba(255,255,255,.3),inset 0 1px 0 rgba(255,255,255,.3),0 0 40px rgba(255,107,53,.2)!important;border-color:rgba(255,255,255,.4)!important}.card-stagger-1{-webkit-animation:slideInUp.8s ease-out.1s both;-moz-animation:slideInUp.8s ease-out.1s both;-o-animation:slideInUp.8s ease-out.1s both;animation:slideInUp.8s ease-out.1s both}.card-stagger-2{-webkit-animation:slideInUp.8s ease-out.2s both;-moz-animation:slideInUp.8s ease-out.2s both;-o-animation:slideInUp.8s ease-out.2s both;animation:slideInUp.8s ease-out.2s both}.card-stagger-3{-webkit-animation:slideInUp.8s ease-out.3s both;-moz-animation:slideInUp.8s ease-out.3s both;-o-animation:slideInUp.8s ease-out.3s both;animation:slideInUp.8s ease-out.3s both}.card-stagger-4{-webkit-animation:slideInUp.8s ease-out.4s both;-moz-animation:slideInUp.8s ease-out.4s both;-o-animation:slideInUp.8s ease-out.4s both;animation:slideInUp.8s ease-out.4s both}@-webkit-keyframes slideInUp{from{opacity:0;-webkit-transform:translatey(60px)scale(.95);transform:translatey(60px)scale(.95)}to{opacity:1;-webkit-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}}@-moz-keyframes slideInUp{from{opacity:0;-moz-transform:translatey(60px)scale(.95);transform:translatey(60px)scale(.95)}to{opacity:1;-moz-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}}@-o-keyframes slideInUp{from{opacity:0;-o-transform:translatey(60px)scale(.95);transform:translatey(60px)scale(.95)}to{opacity:1;-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}}@keyframes slideInUp{from{opacity:0;-webkit-transform:translatey(60px)scale(.95);-moz-transform:translatey(60px)scale(.95);-o-transform:translatey(60px)scale(.95);transform:translatey(60px)scale(.95)}to{opacity:1;-webkit-transform:translatey(0)scale(1);-moz-transform:translatey(0)scale(1);-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}}.card-pulse{-webkit-animation:pulseGlow 3s ease-in-out infinite;-moz-animation:pulseGlow 3s ease-in-out infinite;-o-animation:pulseGlow 3s ease-in-out infinite;animation:pulseGlow 3s ease-in-out infinite}@-webkit-keyframes pulseGlow{0%,100%{-webkit-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1)}50%{-webkit-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3)}}@-moz-keyframes pulseGlow{0%,100%{-moz-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1)}50%{-moz-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3)}}@-o-keyframes pulseGlow{0%,100%{box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1)}50%{box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3)}}@keyframes pulseGlow{0%,100%{-webkit-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1);-moz-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.1)}50%{-webkit-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3);-moz-box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3);box-shadow:0 25px 50px -12px rgba(0,0,0,.25),0 0 0 1px rgba(255,255,255,.2),inset 0 1px 0 rgba(255,255,255,.2),0 0 30px rgba(255,107,53,.3)}}.card-content{position:relative;z-index:2}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                ref: sectionRef,\n                className: \"jsx-d51bc32227e60de8\" + \" \" + \"relative overflow-hidden py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            gridSize: 45,\n                            opacity: 0.06,\n                            color: \"#ff6b35\",\n                            variant: \"premium\",\n                            animated: true,\n                            className: \"absolute inset-0\",\n                            style: {\n                                transform: \"translateY(\".concat(scrollY * 0.1, \"px)\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d51bc32227e60de8\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.05, \"px)\")\n                                            },\n                                            children: [\n                                                \"Enterprise-Grade\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                    children: [\n                                                        ' ',\n                                                        \"AI Infrastructure\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.05\n                                            },\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-4\",\n                                            style: {\n                                                transform: \"translateY(\".concat(scrollY * 0.03, \"px)\")\n                                            },\n                                            children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"space-y-12\",\n                                    children: features.map((feature, index)=>{\n                                        const visibility = cardVisibility[index] || 0;\n                                        const scale = 0.75 + visibility * 0.25; // Scale from 0.75 to 1.0 (more pronounced)\n                                        const cardOpacity = 0.3 + visibility * 0.7; // Opacity from 0.3 to 1.0 (more pronounced)\n                                        // Enhanced glow for most visible card\n                                        const glowIntensity = visibility > 0.7 ? visibility : 0;\n                                        const baseColor = index === 0 ? '37, 99, 235' : index === 1 ? '5, 150, 105' : index === 2 ? '234, 88, 12' : '147, 51, 234';\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            ref: (el)=>{\n                                                cardRefs.current[index] = el;\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"glossy-card rounded-3xl p-8 shadow-2xl border border-white/10\",\n                                            style: {\n                                                backgroundColor: index === 0 ? '#2563eb' : index === 1 ? '#059669' : index === 2 ? '#ea580c' : '#9333ea',\n                                                transform: \"scale(\".concat(scale, \")\"),\n                                                opacity: cardOpacity,\n                                                boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25),\\n                               0 0 0 1px rgba(255, 255, 255, 0.1),\\n                               inset 0 1px 0 rgba(255, 255, 255, 0.1),\\n                               0 0 \".concat(20 + glowIntensity * 30, \"px rgba(\").concat(baseColor, \", \").concat(glowIntensity * 0.4, \")\"),\n                                                borderColor: \"rgba(255, 255, 255, \".concat(0.1 + glowIntensity * 0.2, \")\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d51bc32227e60de8\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d51bc32227e60de8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-12 h-12 bg-black/10 rounded-xl flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                className: \"h-6 w-6 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d51bc32227e60de8\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-2xl font-bold text-white mb-1\",\n                                                                                    children: feature.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 324,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-sm text-white/70 font-medium\",\n                                                                                    children: feature.subtitle\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-lg text-white/90 mb-6 leading-relaxed\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"space-y-3\",\n                                                                    children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"jsx-d51bc32227e60de8\" + \" \" + \"flex items-start gap-3 text-white/80\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d51bc32227e60de8\" + \" \" + \"text-sm leading-relaxed\",\n                                                                                    children: detail\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 341,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d51bc32227e60de8\" + \" \" + \"flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-full aspect-[3/2] bg-black/20 rounded-2xl border border-white/10 flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Smart_AI_Routing.png\",\n                                                                        alt: \"Smart AI Routing\",\n                                                                        className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Enterprise_Security.png\",\n                                                                        alt: \"Enterprise Security\",\n                                                                        className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/Cost_Optimization.png\",\n                                                                        alt: \"Cost Optimization\",\n                                                                        className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: \"/300+_AI_Models.png\",\n                                                                        alt: \"300+ AI Models\",\n                                                                        className: \"jsx-d51bc32227e60de8\" + \" \" + \"w-full h-full object-cover rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeaturesSection, \"vslNTT3t6LgJARFE/wTmyqHACfE=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});